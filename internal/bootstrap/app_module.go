package bootstrap

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/pkg/server"
)

type AppModule struct {
	Server *server.Server
}

func SetupApp(api *APIModule, port string) *AppModule {
	srv := server.NewServer(api.Router, port)

	return &AppModule{
		Server: srv,
	}
}

func (a *AppModule) Start() {
	go func() {
		if err := a.Server.Start(); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := a.Server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exiting")
}
