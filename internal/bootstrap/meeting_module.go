package bootstrap

import (
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"

	"git.inet.co.th/meeting-dashboard-backend/config"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/jwt"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"

	sessionstore "git.inet.co.th/meeting-dashboard-backend/internal/auth/store"
	userstore "git.inet.co.th/meeting-dashboard-backend/internal/users/store"

	mt_h_v1 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/handler"
	mt_sv_v1 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/service"
	mt_st_v1 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/store"

	mt_h_v2 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v2/handler"
	mt_sv_v2 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v2/service"
	mt_st_v2 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v2/store"

	mt_h_v3 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v3/handler"
	mt_sv_v3 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v3/service"
	mt_st_v3 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v3/store"

	mt_h_v4 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/handler"
	mt_sv_v4 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/service"
	mt_st_v4 "git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/store"
)

type MeetingModule struct {
	MeetingHandlerV1 mt_h_v1.MeetingHandler
	MeetingHandlerV2 mt_h_v2.MeetingHandler
	MeetingHandlerV3 mt_h_v3.MeetingHandler
	MeetingHandlerV4 mt_h_v4.MeetingHandler
	CommentHandler   mt_h_v1.CommentHandler
	StatusHandler    mt_h_v1.StatusHandler
	FilterHandler    mt_h_v1.FilterHandler
	DropdownHandler  mt_h_v4.DropdownHandler
}

func SetupMeeting(db *mongo.Database, cfg config.Config) *MeetingModule {
	jwtCfg := jwt.Config{
		SecretKey:     cfg.JWTSecretKey,
		Expiry:        cfg.JWTExpiry,
		Version:       cfg.JWTVersion,
		RefreshExpiry: cfg.JWTRefreshExpiry,
	}
	jwtService := jwt.NewService(jwtCfg)

	userStore := userstore.NewUserStore(db)
	sessionStore := sessionstore.NewSessionStore(db)

	authMiddleware := middleware.NewAuthMiddleware(
		jwtService,
		sessionStore,
		userStore,
	)

	//V1
	meetingStoreV1 := mt_st_v1.NewMeetingStore(db)
	meetingServiceV1 := mt_sv_v1.NewMeetingService(meetingStoreV1)
	meetingHandlerV1 := mt_h_v1.NewMeetingHandler(meetingServiceV1, authMiddleware)

	commentStore := mt_st_v1.NewCommentStore(db)
	commentService := mt_sv_v1.NewCommentService(commentStore, meetingStoreV1)
	commentHandler := mt_h_v1.NewCommentHandler(commentService, authMiddleware)

	statusStore := mt_st_v1.NewStatusStore(db)
	statusService := mt_sv_v1.NewStatusService(statusStore, meetingStoreV1)
	statusHandler := mt_h_v1.NewStatusHandlerr(statusService, authMiddleware)

	filterStore := mt_st_v1.NewFilterStore(db)
	filterService := mt_sv_v1.NewFilterService(filterStore)
	filterHandler := mt_h_v1.NewFilterHandler(filterService, authMiddleware)

	//V2
	meetingStoreV2 := mt_st_v2.NewMeetingStore(db)
	meetingServiceV2 := mt_sv_v2.NewMeetingService(meetingStoreV2)
	meetingHandlerV2 := mt_h_v2.NewMeetingHandler(meetingServiceV2, authMiddleware)

	//V3
	meetingStoreV3 := mt_st_v3.NewMeetingStore(db)
	meetingServiceV3 := mt_sv_v3.NewMeetingService(meetingStoreV3)
	meetingHandlerV3 := mt_h_v3.NewMeetingHandler(meetingServiceV3, authMiddleware)

	//V4
	meetingStoreV4 := mt_st_v4.NewMeetingStore(db)
	meetingServiceV4 := mt_sv_v4.NewMeetingService(meetingStoreV4)
	meetingHandlerV4 := mt_h_v4.NewMeetingHandler(meetingServiceV4, authMiddleware)

	dropdownStore := mt_st_v4.NewDropdownStore(db)
	dropdownService := mt_sv_v4.NewDropdownService(dropdownStore)
	dropdownHandler := mt_h_v4.NewDropdownHandler(dropdownService, authMiddleware)

	return &MeetingModule{
		MeetingHandlerV1: meetingHandlerV1,
		MeetingHandlerV2: meetingHandlerV2,
		MeetingHandlerV3: meetingHandlerV3,
		MeetingHandlerV4: meetingHandlerV4,
		CommentHandler:   commentHandler,
		StatusHandler:    statusHandler,
		FilterHandler:    filterHandler,
		DropdownHandler:  dropdownHandler,
	}
}

func (m *MeetingModule) RegisterRoutesV1(r *gin.RouterGroup) {
	meetingRoutes := r.Group("/meeting")
	m.MeetingHandlerV1.RegisterRoutes(meetingRoutes)
	m.CommentHandler.RegisterRoutes(meetingRoutes)
	m.StatusHandler.RegisterRoutes(meetingRoutes)
	m.FilterHandler.RegisterRoutes(meetingRoutes)
}

func (m *MeetingModule) RegisterRoutesV2(r *gin.RouterGroup) {
	meetingRoutes := r.Group("/meeting")
	m.MeetingHandlerV2.RegisterRoutes(meetingRoutes)
}

func (m *MeetingModule) RegisterRoutesV3(r *gin.RouterGroup) {
	meetingRoutes := r.Group("/meeting")
	m.MeetingHandlerV3.RegisterRoutes(meetingRoutes)
}

func (m *MeetingModule) RegisterRoutesV4(r *gin.RouterGroup) {
	meetingRoutes := r.Group("/meeting")
	m.MeetingHandlerV4.RegisterRoutes(meetingRoutes)
	m.DropdownHandler.RegisterRoutes(meetingRoutes)
}
