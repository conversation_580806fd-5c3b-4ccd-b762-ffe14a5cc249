package bootstrap

import (
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"

	"git.inet.co.th/meeting-dashboard-backend/config"
	"git.inet.co.th/meeting-dashboard-backend/internal/migration/handler"
	"git.inet.co.th/meeting-dashboard-backend/internal/migration/provider"
	"git.inet.co.th/meeting-dashboard-backend/internal/migration/service"
	"git.inet.co.th/meeting-dashboard-backend/internal/migration/store"
)

type MigrationModule struct {
	Service service.MigrateService
	Handler handler.MigrateHandler
}

func SetupMigration(db *mongo.Database, cfg config.Config) *MigrationModule {
	saleProvider := provider.NewProvider(cfg)
	migrateStore := store.NewMigrateStore(db)

	migrateService := service.NewMigrateService(
		saleProvider,
		migrateStore,
	)

	migrateHandler := handler.NewMigrateHandler(migrateService)

	return &MigrationModule{
		Service: migrateService,
		Handler: migrateHandler,
	}
}

func (m *MigrationModule) RegisterRoutes(r *gin.RouterGroup) {
	migrateRoutes := r.Group("/migration")
	m.Handler.RegisterRoutes(migrateRoutes)
}
