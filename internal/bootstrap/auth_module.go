package bootstrap

import (
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"

	"git.inet.co.th/meeting-dashboard-backend/config"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/handler"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/jwt"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/provider/oneth"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/service"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/store"

	userstore "git.inet.co.th/meeting-dashboard-backend/internal/users/store"
)

type AuthModule struct {
	Handler handler.AuthHandler
}

func SetupAuth(db *mongo.Database, cfg config.Config) *AuthModule {
	userRepo := userstore.NewUserStore(db)
	sessionRepo := store.NewSessionStore(db)

	oneThCfg := oneth.Config{
		URL:      cfg.OneThURL,
		ClientID: cfg.OneThClientID,
		Secret:   cfg.OneThSecret,
	}
	oneThService := oneth.NewService(oneThCfg)

	jwtCfg := jwt.Config{
		SecretKey:     cfg.JWTSecretKey,
		Expiry:        cfg.JWTExpiry,
		Version:       cfg.JWTVersion,
		RefreshExpiry: cfg.JWTRefreshExpiry,
	}
	jwtService := jwt.NewService(jwtCfg)

	authService := service.NewAuthService(
		userRepo,
		sessionRepo,
		jwtService,
		oneThService,
	)

	authHandler := handler.NewAuthHandler(authService, cfg.RSAPrivateKeyPEM)

	return &AuthModule{
		Handler: authHandler,
	}
}

func (a *AuthModule) RegisterRoutes(r *gin.RouterGroup) {
	authRoutes := r.Group("/auth")
	a.Handler.RegisterRoutes(authRoutes)
}
