package bootstrap

import (
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"

	"git.inet.co.th/meeting-dashboard-backend/config"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/jwt"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/handler"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/provider/oneth"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/provider/ra"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/service"

	sessionstore "git.inet.co.th/meeting-dashboard-backend/internal/auth/store"
	userstore "git.inet.co.th/meeting-dashboard-backend/internal/users/store"
)

type UserModule struct {
	Handler handler.UserHandler
}

func SetupUser(db *mongo.Database, cfg config.Config) *UserModule {
	jwtCfg := jwt.Config{
		SecretKey:     cfg.JWTSecretKey,
		Expiry:        cfg.JWTExpiry,
		Version:       cfg.JWTVersion,
		RefreshExpiry: cfg.JWTRefreshExpiry,
	}
	jwtService := jwt.NewService(jwtCfg)

	userRepo := userstore.NewUserStore(db)
	sessionStore := sessionstore.NewSessionStore(db)
	oneThProvider := oneth.NewProvider(cfg)
	raProvider := ra.NewProvider(cfg)

	userService := service.NewUserService(
		jwtService,
		userRepo,
		sessionStore,
		oneThProvider,
		raProvider,
	)

	authMiddleware := middleware.NewAuthMiddleware(
		jwtService,
		sessionStore,
		userRepo,
	)

	userHandler := handler.NewUserHandler(userService, authMiddleware)

	return &UserModule{
		Handler: userHandler,
	}
}

func (u *UserModule) RegisterRoutes(r *gin.RouterGroup) {
	userRoutes := r.Group("/users")
	u.Handler.RegisterRoutes(userRoutes)
}
