package bootstrap

import (
	"github.com/gin-gonic/gin"

	"git.inet.co.th/meeting-dashboard-backend/pkg/router"
)

type APIModule struct {
	Router *router.Router
}

func SetupAPI(authModule *AuthModule, userModule *UserModule, migrationModule *MigrationModule, meetingModule *MeetingModule) *APIModule {
	r := router.NewRouter()

	apiV1 := r.Group("/v1")

	authModule.RegisterRoutes(apiV1)
	userModule.RegisterRoutes(apiV1)
	migrationModule.RegisterRoutes(apiV1)
	meetingModule.RegisterRoutesV1(apiV1)

	apiV2 := r.Group("/v2")
	meetingModule.RegisterRoutesV2(apiV2)

	apiV3 := r.Group("/v3")
	meetingModule.RegisterRoutesV3(apiV3)

	apiV4 := r.Group("/v4")
	meetingModule.RegisterRoutesV4(apiV4)

	r.Engine().GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "Meeting Dashboard API is running",
		})
	})

	return &APIModule{
		Router: r,
	}
}
