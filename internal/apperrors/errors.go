package apperrors

import "fmt"

var (
	ErrNotFound             = fmt.<PERSON><PERSON><PERSON>("resource not found")
	ErrUnauthorized         = fmt.<PERSON><PERSON><PERSON>("unauthorized access")
	ErrForbidden            = fmt.Errorf("forbidden access")
	ErrDuplicateEntry       = fmt.<PERSON><PERSON>rf("duplicate entry")
	ErrTokenBlacklisted     = fmt.Errorf("token has been revoked")
	ErrSessionInvalid       = fmt.Errorf("session is invalid or expired")
	ErrTokenVersionMismatch = fmt.Errorf("token version is outdated")
	ErrRefreshTokenInvalid  = fmt.Errorf("refresh token is invalid")
	ErrInvalidTokenType     = fmt.Errorf("invalid token type")
	ErrInvalidCredentials   = fmt.Errorf("invalid credentials")
	ErrInternalServerError  = fmt.Errorf("internal server error")
)

type AuthError struct {
	Message string
}

func (e *AuthError) Error() string {
	return e.Message
}

func NewAuthError(message string) *AuthError {
	return &AuthError{Message: message}
}

type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation failed for field '%s': %s", e.Field, e.Message)
}

func NewValidationError(message string) error {
	return &ValidationError{Message: message}
}

type BadRequestError struct {
	Message string
}

func (e *BadRequestError) Error() string {
	return e.Message
}
func NewBadRequestError(message string) error {
	return &BadRequestError{Message: message}
}

type NotFoundError struct {
	Resource string
	ID       string
}

func (e *NotFoundError) Error() string {
	return fmt.Sprintf("%s not found with ID: %s", e.Resource, e.ID)
}

func NewNotFoundError(resource, id string) error {
	return &NotFoundError{Resource: resource, ID: id}
}

type InternalServerError struct {
	Message string
}

func (e *InternalServerError) Error() string {
	return e.Message
}

func NewInternalServerError(message string) error {
	return &InternalServerError{Message: message}
}
