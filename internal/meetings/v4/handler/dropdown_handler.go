package handler

import (
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/service"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"
	"git.inet.co.th/meeting-dashboard-backend/pkg/response"
	"github.com/gin-gonic/gin"
)

type DropdownHandlerImpl struct {
	dropdownService service.DropdownService
	authMiddleware  *middleware.AuthMiddleware
}

func NewDropdownHandler(dropdownService service.DropdownService, authMiddleware *middleware.AuthMiddleware) DropdownHandler {
	return &DropdownHandlerImpl{
		dropdownService: dropdownService,
		authMiddleware:  authMiddleware,
	}
}
func (h *DropdownHandlerImpl) GetDropdownSaleName(c *gin.Context) {
	ctx := c.Request.Context()
	data, err := h.dropdownService.GetDropdownSaleName(ctx)
	if err != nil {
		c.Error(err)
		return
	}

	response.SendOK(c, "Get dropdown salename successfully", data)
}

func (h *DropdownHandlerImpl) GetDropdownServiceName(c *gin.Context) {
	ctx := c.Request.Context()
	data, err := h.dropdownService.GetDropdownServiceName(ctx)
	if err != nil {
		c.Error(err)
		return
	}

	response.SendOK(c, "Get dropdown servicename successfully", data)
}

func (h *DropdownHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	r.Use(h.authMiddleware.RequireAuth())

	dropdownGroup := r.Group("/dropdown")
	{
		dropdownGroup.GET("/sale_name", h.GetDropdownSaleName)
		dropdownGroup.GET("/service_name", h.GetDropdownServiceName)
	}
}
