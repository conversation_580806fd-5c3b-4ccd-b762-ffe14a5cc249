package store

import (
	"context"
	"net/http"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/data"
	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)


type DropdownStoreImpl struct {
	meetingJoinCollection *mongo.Collection
}

func NewDropdownStore(db *mongo.Database) DropdownStore {
	return &DropdownStoreImpl{
		meetingJoinCollection: db.Collection(data.MeetingJoinCollectionName),
	}
}

func (s *DropdownStoreImpl) GetDropdownSaleName(ctx context.Context) ([]string, error) {
	type DropdownData struct {
		SaleNames []string `bson:"sale_name_th"`
	}
	matchStage := bson.D{
		{Key: "$match", Value: bson.M{
			"sale_name_th": bson.M{
				"$exists": true,
				"$ne":     "",
			},
		}},
	}

	groupStage := bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id":             nil,
				"uniqueSaleNames": bson.M{"$addToSet": "$sale_name_th"},
			},
		},
	}

	projectStage := bson.D{
		{
			Key: "$project",
			Value: bson.M{
				"_id":          0,
				"sale_name_th": "$uniqueSaleNames",
			},
		},
	}

	pipeline := mongo.Pipeline{matchStage, groupStage, projectStage}

	cursor, err := s.meetingJoinCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	defer cursor.Close(ctx)

	var dropdown []DropdownData
	if err := cursor.All(ctx, &dropdown); err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}
	if len(dropdown) == 0 {
		return nil, apperrors.ErrNotFound
	}

	return dropdown[0].SaleNames, nil
}

func (s *DropdownStoreImpl) GetDropdownServiceName(ctx context.Context) ([]string, error) {
	type DropdownData struct {
		ServiceName []string `bson:"service_name"`
	}

	unwindStage := bson.D{
		{
			Key:   "$unwind",
			Value: "$StageServices",
		},
	}

	matchStage := bson.D{
		{Key: "$match", Value: bson.M{
			"StageServices.service_name": bson.M{
				"$exists": true,
				"$ne":     "",
			},
		}},
	}

	groupStage := bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id":                nil,
				"uniqueServiceNames": bson.M{"$addToSet": "$StageServices.service_name"},
			},
		},
	}

	projectStage := bson.D{
		{
			Key: "$project",
			Value: bson.M{
				"_id":          0,
				"service_name": "$uniqueServiceNames",
			},
		},
	}

	pipeline := mongo.Pipeline{unwindStage, matchStage, groupStage, projectStage}

	cursor, err := s.meetingJoinCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	defer cursor.Close(ctx)

	var dropdown []DropdownData
	if err := cursor.All(ctx, &dropdown); err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}
	if len(dropdown) == 0 {
		return nil, apperrors.ErrNotFound
	}

	return dropdown[0].ServiceName, nil
}
