package store

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/model"
)

type MeetingStore interface {
	FetchMeetingData(ctx context.Context, body dto.MeetingRequest) ([]*model.MeetingRawData, error)
	CountMeetingData(ctx context.Context, body dto.MeetingRequest) (int64, error)
}

type DropdownStore interface {
	GetDropdownSaleName(ctx context.Context) ([]string, error)
	GetDropdownServiceName(ctx context.Context) ([]string, error)
}
