package store

import (
	"context"
	"net/http"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/data"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/utils"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/model"

	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
)

type MeetingStoreImpl struct {
	meetingCollection     *mongo.Collection
	metaDataCollection    *mongo.Collection
	meetingJoinCollection *mongo.Collection
}

func NewMeetingStore(db *mongo.Database) MeetingStore {
	return &MeetingStoreImpl{
		meetingCollection:     db.Collection(data.MeetingCollectionName),
		metaDataCollection:    db.Collection(data.MetaDataCollectionName),
		meetingJoinCollection: db.Collection(data.MeetingJoinCollectionName),
	}
}

func (s *MeetingStoreImpl) FetchMeetingData(ctx context.Context, body dto.MeetingRequest) ([]*model.MeetingRawData, error) {
	pipeline := mongo.Pipeline{}
	pipeline = append(pipeline,
		utils.GenerateMatchStage(body),
		s.groupStage(),
		s.projectStage(),
		utils.GenerateSort(body),
	)

	if body.Page > 0 && body.Limit > 0 {
		pipeline = append(pipeline,
			bson.D{{Key: "$skip", Value: (body.Page - 1) * body.Limit}},
			bson.D{{Key: "$limit", Value: body.Limit}},
		)
	}

	cursor, err := s.meetingJoinCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	defer cursor.Close(ctx)

	data := make([]*model.MeetingRawData, 0)
	if err := cursor.All(ctx, &data); err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}

	return data, nil
}

func (s *MeetingStoreImpl) CountMeetingData(ctx context.Context, body dto.MeetingRequest) (int64, error) {
	matchStage := utils.GenerateMatchStage(body)

	groupStage := bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id": "$meet_id",
			},
		},
	}

	pipeline := mongo.Pipeline{
		matchStage,
		groupStage,
		bson.D{{Key: "$count", Value: "total"}},
	}

	cursor, err := s.meetingJoinCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	defer cursor.Close(ctx)

	var countResult struct {
		Total int `bson:"total"`
	}
	if cursor.Next(ctx) {
		if err := cursor.Decode(&countResult); err != nil {
			return 0, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
		}
	}

	return int64(countResult.Total), nil
}

func (s *MeetingStoreImpl) fullAddress() bson.M {
	return bson.M{
		"$first": bson.M{
			"$concat": bson.A{
				bson.M{"$ifNull": bson.A{bson.M{"$trim": bson.M{"input": "$cus_address_company"}}, ""}},
				" ",
				bson.M{"$ifNull": bson.A{bson.M{"$trim": bson.M{"input": "$cus_districts"}}, ""}},
				" ",
				bson.M{"$ifNull": bson.A{bson.M{"$trim": bson.M{"input": "$cus_amphurs"}}, ""}},
				" ",
				bson.M{"$ifNull": bson.A{bson.M{"$trim": bson.M{"input": "$cus_provinces"}}, ""}},
				" ",
				bson.M{"$ifNull": bson.A{bson.M{"$trim": bson.M{"input": "$cus_post"}}, ""}},
			},
		},
	}
}

func (s *MeetingStoreImpl) groupStage() bson.D {
	return bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id":                                   "$meet_id",
				"cus_id":                                utils.First("cus_id"),
				"add_meet_date":                         utils.First("add_meet_date"),
				"add_meet_time":                         utils.First("add_meet_time"),
				"meet_erp_code":                         utils.First("meet_erp_code"),
				"company_name_th":                       utils.First("company_name_th"),
				"meet_block_name":                       utils.First("meet_block_name"),
				"meet_sum_grade":                        utils.First("meet_sum_grade"),
				"cus_conference":                        utils.First("cus_conference"),
				"cus_other_desc":                        utils.First("cus_other_desc"),
				"type_of_business_id":                   utils.First("type_of_business_id"),
				"cus_clc":                               utils.First("cus_clc"),
				"ps_clc_name":                           utils.First("ps_clc_name"),
				"cus_visit":                             utils.First("cus_visit"),
				"sale_type_name":                        utils.First("sale_type_name"),
				"ps_visit_name":                         utils.First("ps_visit_name"),
				"cus_on_site_other":                     utils.First("cus_on_site_other"),
				"cus_plc":                               utils.First("cus_plc"),
				"ps_plc_name":                           utils.First("ps_plc_name"),
				"sale_name_th":                          utils.First("sale_name_th"),
				"partner_type":                          utils.First("partner_type"),
				"meet_department_sale":                  utils.First("meet_department_sale"),
				"cus_name":                              utils.First("cus_name"),
				"cus_position":                          utils.First("cus_position"),
				"cus_phone":                             utils.First("cus_phone"),
				"cus_email":                             utils.First("cus_email"),
				"cus_address_company":                   utils.First("cus_address_company"),
				"cus_districts":                         utils.First("cus_districts"),
				"cus_amphurs":                           utils.First("cus_amphurs"),
				"cus_provinces":                         utils.First("cus_provinces"),
				"cus_post":                              utils.First("cus_post"),
				"full_address":                          s.fullAddress(),
				"private_customer_transform_businesses": utils.First("private_customer_transform_businesses"),
				"meet_status":                           utils.First("meet_status"),
				"meet_create_at":                        utils.First("meet_create_at"),
				"site":                                  utils.First("site"),
				"comment":                               utils.First("comment"),
				"meeting_service":                       utils.First("meeting_service"),
				"status":                                utils.First("status"),
				"sale_mom_desc":                         utils.First("sale_mom_desc"),
				"presale_mom_desc":                      utils.First("presale_mom_desc"),
				"StageServices":                         utils.First("StageServices"),
				"tax_id":                                utils.First("tax_id"),
				"partner_erp":                           utils.First("partner_erp"),
				"service_erp":                           utils.First("service_erp"),
				"partner_crm":                           utils.First("partner_crm"),
				"service_crm":                           utils.First("service_crm"),
				"partner_hrm":                           utils.First("partner_hrm"),
				"service_hrm":                           utils.First("service_hrm"),
				"partner_supply_chain":                  utils.First("partner_supply_chain"),
				"service_supply_chain":                  utils.First("service_supply_chain"),
				"partner_workflow":                      utils.First("partner_workflow"),
				"service_workflow":                      utils.First("service_workflow"),
				"partner_e_commerce_web":                utils.First("partner_e_commerce_web"),
				"service_e_commerce_web":                utils.First("service_e_commerce_web"),
			},
		},
	}
}

func (s *MeetingStoreImpl) projectStage() bson.D {
	var (
		// ลบคำทั่วไปที่มักปรากฏในฟิลด์ cus_conference เช่น "Online", "Onsite", และ "ที่อยู่บริษัทลูกค้า"
		// โดยใช้ $reduce ทำการวนซ้ำและแทนที่ด้วย string ว่าง ("") ทีละคำ
		cusConferenceReduce = bson.M{
			"$reduce": bson.M{
				"input": bson.A{
					"Online",
					"Onsite",
					"ที่อยู่บริษัทลูกค้า",
				},
				"initialValue": "$cus_conference",
				"in": bson.M{
					"$replaceAll": bson.M{
						"input":       "$$value",
						"find":        "$$this",
						"replacement": "",
					},
				},
			},
		}

		// ลบ prefix ข้อความอัตโนมัติ เช่น "Service ที่เข้าไปนำเสนอคือ" เพื่อให้เหลือเฉพาะคำบรรยายที่เขียนเอง
		// โดยใช้ $reduce และ $replaceAll เพื่อให้ข้อความสั้นลงและชัดเจนขึ้น
		cusOtherDescReduce = bson.M{
			"$reduce": bson.M{
				"input": bson.A{
					"Service ที่เข้าไปนำเสนอคือ",
				},
				"initialValue": "$cus_other_desc",
				"in": bson.M{
					"$replaceAll": bson.M{
						"input":       "$$value",
						"find":        "$$this",
						"replacement": "",
					},
				},
			},
		}

		// ดึงค่าชื่อ service ทั้งหมดจาก StageServices
		// โดยใช้ $map เพื่อสร้าง array ของ $$svc.service_name
		serviceNameMap = bson.M{
			"$map": bson.M{
				"input": "$StageServices",
				"as":    "svc",
				"in":    "$$svc.service_name",
			},
		}

		// สร้าง array ของ status service description ในรูปแบบ "status-description"
		// เช่น "Completed-ติดตั้งเสร็จแล้ว" โดยใช้ $map และ $concat
		statusServiceMap = bson.M{
			"$map": bson.M{
				"input": "$StageServices",
				"as":    "svc",
				"in": bson.M{
					"$concat": bson.A{
						"$$svc.service_status",
						"-",
						"$$svc.service_status_description",
					},
				},
			},
		}

		// แปลง add_meet_date และ add_meet_time ให้เป็น ISODate ด้วย $dateFromString
		// โดยใช้ $concat เชื่อมวันที่และเวลาก่อน
		meetDateTime = bson.M{
			"$dateFromString": bson.M{
				"dateString": bson.M{
					"$concat": bson.A{
						"$add_meet_date", "T", "$add_meet_time",
					},
				},
				"timezone": "Asia/Bangkok",
			},
		}

		// แปลงวันที่จาก string เป็นรูปแบบ dd/mm/yyyy ด้วย $dateToString
		// ใช้ร่วมกับ $dateFromString เพื่อให้แน่ใจว่า input มีรูปแบบ date ที่ถูกต้อง
		formatDate = utils.DateFormat("add_meet_date")

		// แปลงวันที่ meet_create_at จาก string ให้เป็นรูปแบบ dd/mm/yyyy
		// โดยใช้ $dateFromString แปลงเป็น date object ก่อน แล้วจึงใช้ $dateToString เพื่อ format ให้ตรงตามรูปแบบที่ต้องการ
		meetCreateAtFormat = utils.DateFormat("meet_create_at")
	)

	return bson.D{
		{
			Key: "$project",
			Value: bson.M{
				"meet_id":                               "$_id",
				"cus_id":                                1,
				"add_meet_date":                         1,
				"add_meet_time":                         1,
				"meet_erp_code":                         1,
				"company_name_th":                       1,
				"meet_block_name":                       1,
				"meet_sum_grade":                        1,
				"cus_conference":                        cusConferenceReduce,
				"cus_other_desc":                        cusOtherDescReduce,
				"type_of_business_id":                   1,
				"cus_clc":                               1,
				"ps_clc_name":                           1,
				"cus_visit":                             1,
				"sale_type_name":                        1,
				"ps_visit_name":                         1,
				"cus_on_site_other":                     1,
				"cus_plc":                               1,
				"ps_plc_name":                           1,
				"sale_name_th":                          1,
				"ps_privilege_name":                     1,
				"meet_department_sale":                  1,
				"cus_name":                              1,
				"cus_position":                          1,
				"cus_phone":                             1,
				"cus_email":                             1,
				"full_address":                          1,
				"cus_address_company":                   1,
				"cus_districts":                         1,
				"cus_amphurs":                           1,
				"cus_provinces":                         1,
				"cus_post":                              1,
				"private_customer_transform_businesses": 1,
				"meet_status":                           1,
				"meet_create_at":                        1,
				"partner_type":                          1,
				"status":                                1,
				"site":                                  1,
				"meeting_service":                       1,
				"comment":                               1,
				"sale_mom_desc":                         1,
				"presale_mom_desc":                      1,
				"tax_id":                                1,
				"partner_erp":                           1,
				"service_erp":                           1,
				"partner_crm":                           1,
				"service_crm":                           1,
				"partner_hrm":                           1,
				"service_hrm":                           1,
				"partner_supply_chain":                  1,
				"service_supply_chain":                  1,
				"partner_workflow":                      1,
				"service_workflow":                      1,
				"partner_e_commerce_web":                1,
				"service_e_commerce_web":                1,
				"service_name":                          serviceNameMap,
				"status_service":                        statusServiceMap,
				"meet_datetime":                         meetDateTime,
				"format_date":                           formatDate,
				"meet_create_at_format":                 meetCreateAtFormat,
			},
		},
	}
}
