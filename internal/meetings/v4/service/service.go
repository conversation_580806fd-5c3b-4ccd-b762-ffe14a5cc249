package service

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/dto"
)

type MeetingService interface {
	FetchMeetingData(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingResponse, error)
	ExportDataToExcel(ctx context.Context, body dto.MeetingRequest, filename string) ([]byte, error)
}

type DropdownService interface {
	GetDropdownSaleName(ctx context.Context) ([]string, error)
	GetDropdownServiceName(ctx context.Context) ([]string, error)
}
