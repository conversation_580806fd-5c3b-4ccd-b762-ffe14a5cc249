package service

import (
	"context"
	"sort"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/store"
)

type DropdownServiceImpl struct {
	dropdownStore store.DropdownStore
}

func NewDropdownService(
	dropdownStore store.DropdownStore,
) DropdownService {
	return &DropdownServiceImpl{
		dropdownStore: dropdownStore,
	}
}

func (s *DropdownServiceImpl) GetDropdownSaleName(ctx context.Context) ([]string, error) {
	saleNames, err := s.dropdownStore.GetDropdownSaleName(ctx)
	if err != nil {
		return nil, err
	}

	sort.Strings(saleNames)

	return saleNames, nil
}

func (s *DropdownServiceImpl) GetDropdownServiceName(ctx context.Context) ([]string, error) {
	serviceNames, err := s.dropdownStore.GetDropdownServiceName(ctx)
	if err != nil {
		return nil, err
	}

	sort.Strings(serviceNames)

	return serviceNames, nil
}
