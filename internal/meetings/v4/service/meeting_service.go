package service

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/xuri/excelize/v2"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/data"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/utils"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/store"

	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
)

type MeetingServiceImpl struct {
	meetingStore store.MeetingStore
}

func NewMeetingService(meetingStore store.MeetingStore) MeetingService {
	return &MeetingServiceImpl{
		meetingStore: meetingStore,
	}
}

func (s *MeetingServiceImpl) FetchMeetingData(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingResponse, error) {
	meetData, err := s.meetingStore.FetchMeetingData(ctx, body)
	if err != nil {
		return nil, err
	}

	meetDTOs := make([]dto.MeetingData, len(meetData))
	for i, data := range meetData {
		dtoItem := *dto.ToMeetingData(data)

		// 🔧 ตัดคำว่า "เหตุผลอื่น ๆ คือ " , ไม่ได้ระบุ ออกจาก CustomerOtherDescription
		dtoItem.CustomerOtherDescription = utils.CleanCustomerOtherDescription(dtoItem.CustomerOtherDescription)

		meetDTOs[i] = dtoItem
	}

	meetCountData, err := s.meetingStore.CountMeetingData(ctx, body)
	if err != nil {
		return nil, err
	}

	return &dto.MeetingResponse{
		Success:    true,
		TotalCount: int(meetCountData),
		Data:       meetDTOs,
	}, nil
}

func (s *MeetingServiceImpl) ExportDataToExcel(ctx context.Context, body dto.MeetingRequest, filename string) ([]byte, error) {
	meetData, err := s.meetingStore.FetchMeetingData(ctx, body)
	if err != nil {
		return nil, err
	}

	f := excelize.NewFile()
	sheetName := "Meeting"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "failed to create new sheet", err)
	}
	f.SetActiveSheet(index)

	// Headers
	for colIndex, header := range data.Headers {
		cell, _ := excelize.CoordinatesToCellName(colIndex+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// Data
	for rowIndex, data := range meetData {
		cellMap := map[string]any{
			"A":  data.MeetDateFormat,
			"B":  data.MeetTime,
			"C":  data.MeetERPCode,
			"D":  data.CompanyName,
			"E":  data.Block,
			"F":  data.MeetSumGrade,
			"G":  data.MeetingChannel,
			"H":  data.CustomerConference,
			"I":  utils.CleanCustomerOtherDescription(data.CustomerOtherDescription),
			"J":  data.TypeOfBusiness,
			"K":  strings.Join(data.CustomerCLC, ", "),
			"L":  data.PresaleCLCName,
			"M":  strings.Join(data.CustomerVisit, ", "),
			"N":  data.PresaleVisitName,
			"O":  data.CustomerOnsiteOther,
			"P":  strings.Join(data.CustomerPLC, ", "),
			"Q":  data.PresalePLCName,
			"R":  data.PartnerType,
			"S":  data.SaleName,
			"T":  data.BlockTeamSale,
			"U":  data.CustomerName,
			"V":  data.CustomerPosition,
			"W":  data.CustomerPhone,
			"X":  data.CustomerEmail,
			"Y":  data.FullAddress,
			"Z":  data.BillHeader,
			"AA": data.MeetStatus,
			"AB": data.MeetingCreatedAtFormat,
			"AC": strings.Join(data.MeetingService, ", "),
			"AD": data.Comment,
			"AE": data.Status,
			"AF": data.TaxId,
			"AG": data.SaleMOM,
			"AH": data.PreSaleMOM,
			"AI": strings.Join(data.ServiceName, ", "),
			"AJ": strings.Join(data.StatusService, ", "),
			"AK": data.PartnerERP,
			"AL": data.ServiceERP,
			"AM": data.PartnerCRM,
			"AN": data.ServiceCRM,
			"AO": data.PartnerHRM,
			"AP": data.ServiceHRM,
			"AQ": data.PartnerSupplyChain,
			"AR": data.ServiceSupplyChain,
			"AS": data.PartnerWorkflow,
			"AT": data.ServiceWorkflow,
			"AU": data.PartnerECommerceWeb,
			"AV": data.ServiceECommerceWeb,
			"AW": data.Privilege,
			"AX": data.PresalePrivilegeName,
		}

		for col, value := range cellMap {
			f.SetCellValue(sheetName, fmt.Sprintf("%s%d", col, rowIndex+2), value)
		}
	}

	utils.AutoAdjustColumnWidth(f, sheetName, 1, 48)

	buf := new(bytes.Buffer)
	if err := f.Write(buf); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}
