package dto

import (
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/model"
)

type SuccessResponse struct {
	Status     string `json:"status"`
	Message    string `json:"message,omitempty"`
	TotalCount int    `json:"total_count"`
	Page       int    `json:"page"`
	Limit      int    `json:"limit"`
	Data       any    `json:"data,omitempty"`
}

type MeetingRequest struct {
	MeetStartDate    string         `json:"meet_start_date"`
	MeetEndDate      string         `json:"meet_end_date"`
	MeetStartTime    string         `json:"meet_start_time"`
	MeetEndTime      string         `json:"meet_end_time"`
	BlockCustomer    []string       `json:"block_customer"`
	BlockTeamSale    []string       `json:"block_team_sale"`
	MeetingService   []string       `json:"meeting_service"`
	CustomerCLC      []string       `json:"customer_clc"`
	CustomerPLC      []string       `json:"customer_plc"`
	PresaleCLCName   []string       `json:"presale_clc_name"`
	PresalePLCName   []string       `json:"presale_plc_name"`
	PresaleVisitName []string       `json:"presale_visit_name"`
	Status           []string       `json:"status"`
	SaleNames        []string       `json:"sale_names"`
	CompanyKeyword   string         `json:"company_keyword"`
	Privileges       []string       `json:"privileges"`
	ServiceNames     []string       `json:"service_names"`
	Sort             map[string]int ` json:"sort"`
	Page             int            `json:"page"`
	Limit            int            `json:"limit"`
}

type MeetingResponse struct {
	Success    bool          `json:"success"`
	TotalCount int           `json:"total_count"`
	Data       []MeetingData `json:"data"`
}

type MeetingData struct {
	MeetID                   int      `json:"meet_id"`
	CustomerID               string   `json:"cus_id"`
	MeetDate                 string   `json:"meet_date"`
	MeetDateFormat           string   `json:"format_date"`
	MeetTime                 string   `json:"meet_time"`
	MeetERPCode              string   `json:"meet_erp_code"`
	CompanyName              string   `json:"company_name"`
	Block                    string   `json:"block"`
	SumGrade                 string   `json:"sum_grade"`
	MeetingChannel           string   `json:"meeting_channel"`
	CustomerConference       string   `json:"customer_conference"`
	CustomerOtherDescription string   `json:"customer_other_description"`
	TypeOfBusiness           string   `json:"type_of_business"`
	CustomerCLC              []string `json:"customer_clc"`
	PresaleCLCName           string   `json:"presale_clc_name"`
	CustomerVisit            []string `json:"customer_visit"`
	PresaleVisitName         string   `json:"presale_visit_name"`
	Privilege                string   `json:"privilege"`
	PresalePrivilegeName     string   `json:"presale_privilege_name"`
	CustomerOnsiteOther      string   `json:"customer_on_site_other"`
	CustomerPLC              []string `json:"customer_plc"`
	PresalePLCName           string   `json:"presale_plc_name"`
	PartnerType              string   `json:"partner_type"`
	SaleName                 string   `json:"sale_name"`
	BlockTeamSale            string   `json:"block_team_sale"`
	CustomerName             string   `json:"customer_name"`
	CustomerPosition         string   `json:"customer_position"`
	CustomerPhone            string   `json:"customer_phone"`
	CustomerEmail            string   `json:"customer_email"`
	FullAddress              string   `json:"full_address"`
	CustomerAddressCompany   string   `json:"cus_address_company"`
	CustomerDistricts        string   `json:"cus_districts"`
	CustomerAmphurs          string   `json:"cus_amphurs"`
	CustomerProvinces        string   `json:"cus_provinces"`
	CustomerPost             string   `json:"cus_post"`
	BillHeader               string   `json:"bill_header"`
	MeetStatus               string   `json:"meet_status"`
	MeetingCreatedAt         string   `json:"meet_create_at"`
	MeetingCreatedAtFormat   string   `json:"meet_create_at_format"`
	MeetingService           []string `json:"meeting_service"`
	Status                   string   `json:"status"`
	Comment                  string   `json:"comment"`
	SaleMOM                  string   `json:"sale_mom_desc"`
	PreSaleMOM               string   `json:"presale_mom_desc"`
	ServiceName              []string `json:"service_name"`
	StatusService            []string `json:"status_service"`
	TaxId                    string   `json:"tax_id"`
	PartnerERP               string   `json:"partner_erp"`
	ServiceERP               string   `json:"service_erp"`
	PartnerCRM               string   `json:"partner_crm"`
	ServiceCRM               string   `json:"service_crm"`
	PartnerHRM               string   `json:"partner_hrm"`
	ServiceHRM               string   `json:"service_hrm"`
	PartnerSupplyChain       string   `json:"partner_supply_chain"`
	ServiceSupplyChain       string   `json:"service_supply_chain"`
	PartnerWorkflow          string   `json:"partner_workflow"`
	ServiceWorkflow          string   `json:"service_workflow"`
	PartnerECommerceWeb      string   `json:"partner_e_commerce_web"`
	ServiceECommerceWeb      string   `json:"service_e_commerce_web"`
}

func ToMeetingData(data *model.MeetingRawData) *MeetingData {
	return &MeetingData{
		MeetID:                   data.MeetID,
		CustomerID:               data.CustomerID,
		MeetDate:                 data.MeetDate,
		MeetDateFormat:           data.MeetDateFormat,
		MeetTime:                 data.MeetTime,
		MeetERPCode:              data.MeetERPCode,
		CompanyName:              data.CompanyName,
		Block:                    data.Block,
		SumGrade:                 data.MeetSumGrade,
		MeetingChannel:           data.MeetingChannel,
		CustomerConference:       data.CustomerConference,
		CustomerOtherDescription: data.CustomerOtherDescription,
		TypeOfBusiness:           data.TypeOfBusiness,
		CustomerCLC:              data.CustomerCLC,
		PresaleCLCName:           data.PresaleCLCName,
		CustomerVisit:            data.CustomerVisit,
		Privilege:                data.Privilege,
		PresalePrivilegeName:     data.PresalePrivilegeName,
		PresaleVisitName:         data.PresaleVisitName,
		CustomerOnsiteOther:      data.CustomerOnsiteOther,
		CustomerPLC:              data.CustomerPLC,
		PresalePLCName:           data.PresalePLCName,
		PartnerType:              data.PartnerType,
		SaleName:                 data.SaleName,
		BlockTeamSale:            data.BlockTeamSale,
		CustomerName:             data.CustomerName,
		CustomerPosition:         data.CustomerPosition,
		CustomerPhone:            data.CustomerPhone,
		CustomerEmail:            data.CustomerEmail,
		FullAddress:              data.FullAddress,
		CustomerAddressCompany:   data.CustomerAddressCompany,
		CustomerDistricts:        data.CustomerDistricts,
		CustomerAmphurs:          data.CustomerAmphurs,
		CustomerProvinces:        data.CustomerProvinces,
		CustomerPost:             data.CustomerPost,
		BillHeader:               data.BillHeader,
		MeetStatus:               data.MeetStatus,
		MeetingCreatedAt:         data.MeetingCreatedAt,
		MeetingCreatedAtFormat:   data.MeetingCreatedAtFormat,
		MeetingService:           data.MeetingService,
		Status:                   data.Status,
		Comment:                  data.Comment,
		SaleMOM:                  data.SaleMOM,
		PreSaleMOM:               data.PreSaleMOM,
		ServiceName:              data.ServiceName,
		StatusService:            data.StatusService,
		TaxId:                    data.TaxId,
		PartnerERP:               data.PartnerERP,
		ServiceERP:               data.ServiceERP,
		PartnerCRM:               data.PartnerCRM,
		ServiceCRM:               data.ServiceCRM,
		PartnerHRM:               data.PartnerHRM,
		ServiceHRM:               data.ServiceHRM,
		PartnerSupplyChain:       data.PartnerSupplyChain,
		ServiceSupplyChain:       data.ServiceSupplyChain,
		PartnerWorkflow:          data.PartnerWorkflow,
		ServiceWorkflow:          data.ServiceWorkflow,
		PartnerECommerceWeb:      data.PartnerECommerceWeb,
		ServiceECommerceWeb:      data.ServiceECommerceWeb,
	}
}
