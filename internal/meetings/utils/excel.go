package utils

import (
	"fmt"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/xuri/excelize/v2"
)

func AutoAdjustColumnWidth(f *excelize.File, sheetName string, startRow int, totalColumns int) {
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return
	}
	endRow := len(rows)

	for col := 1; col <= totalColumns; col++ {
		colLetter := columnNumberToLetter(col)
		colWidth := 0

		header, _ := f.GetCellValue(sheetName, fmt.Sprintf("%s%d", colLetter, startRow))
		header = strings.TrimSpace(header)
		colWidth = utf8.RuneCountInString(header)

		for row := startRow + 1; row <= endRow; row++ {
			cell, _ := f.GetCellValue(sheetName, fmt.Sprintf("%s%d", colLetter, row))
			cell = strings.TrimSpace(cell)
			if utf8.RuneCountInString(cell) > colWidth {
				colWidth = utf8.RuneCountInString(cell)
			}
		}

		if colWidth < 10 {
			colWidth = 10
		} else if colWidth > 80 {
			colWidth = 80
		}

		f.SetColWidth(sheetName, colLetter, colLetter, float64(colWidth+2))
	}
}

func CleanCustomerOtherDescription(input string) string {
	// 0. แทนที่ &nbsp; ด้วยช่องว่างธรรมดา
	input = strings.ReplaceAll(input, "&nbsp;", " ")
	// 1. ลบ prefix "เหตุผลอื่น ๆ คือ " (ไม่ต้องกังวล tag ที่ห้อยมา)
	input = strings.ReplaceAll(input, "เหตุผลอื่น ๆ คือ ", "")

	// 2. ลบ tag HTML ทิ้ง
	input = stripHTMLTags(input)

	// 3. ลบคำว่า "ไม่ได้ระบุ" พร้อม , และช่องว่างรอบข้าง
	re := regexp.MustCompile(`(?i)\s*,?\s*ไม่ได้ระบุ\s*,?\s*`)
	input = re.ReplaceAllString(input, "")

	// 4. Trim ซ้ำอีกรอบ
	return strings.TrimSpace(input)
}

func columnNumberToLetter(colNum int) string {
	// การคำนวณให้รองรับหลายคอลัมน์ (เช่น A, B, ..., Z, AA, AB, ...)
	var colStr string
	for colNum > 0 {
		colNum-- // ลดลง 1 เพื่อให้ทำงานกับการคำนวณ
		colStr = string(rune('A'+colNum%26)) + colStr
		colNum /= 26
	}
	return colStr
}

// stripHTMLTags removes all HTML tags
func stripHTMLTags(input string) string {
	re := regexp.MustCompile(`<[^>]*>`)
	return re.ReplaceAllString(input, "")
}
