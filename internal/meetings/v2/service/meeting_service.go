package service

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"strings"
	"unicode/utf8"

	"github.com/xuri/excelize/v2"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v2/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v2/store"
	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
)

type MeetingServiceImpl struct {
	meetingStore store.MeetingStore
}

func NewMeetingService(
	meetingStore store.MeetingStore,
) MeetingService {
	return &MeetingServiceImpl{
		meetingStore: meetingStore,
	}
}

func (s *MeetingServiceImpl) FetchMeetingData(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingResponse, error) {
	meetData, err := s.meetingStore.FetchMeetingData(ctx, body)
	if err != nil {
		return nil, err
	}

	meetDTOs := make([]dto.MeetingData, len(meetData))
	for i, data := range meetData {
		meetDTOs[i] = *dto.ToMeetingData(data)
	}

	meetCountData, err := s.meetingStore.CountMeetingData(ctx, body)
	if err != nil {
		return nil, err
	}

	return &dto.MeetingResponse{
		Success:    true,
		TotalCount: int(meetCountData),
		Data:       meetDTOs,
	}, nil
}

func (s *MeetingServiceImpl) ExportDataToExcel(ctx context.Context, body dto.MeetingRequest, filename string) ([]byte, error) {

	meetData, err := s.meetingStore.FetchMeetingData(ctx, body)
	if err != nil {
		return nil, err
	}

	f := excelize.NewFile()
	sheetName := "Meeting"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "failed to create new sheet", err)
	}
	f.SetActiveSheet(index)

	// Headers
	headers := []string{
		"Meet Date",
		"Meet Time",
		"Meet ERP Code",
		"Company Name",
		"Block",
		"Sum Grade",
		"Meeting Channel",
		"Customer Conference",
		"Customer Other Description",
		"Type of Business",
		"Customer CLC",
		"Presale CLC Name",
		"Customer Visit",
		"Presale Visit Name",
		"Customer Onsite Other",
		"Customer PLC",
		"Presale PLC Name",
		"Partner Type",
		"Sale Name",
		"Block Team Sale",
		"Customer Name",
		"Customer Position",
		"Customer Phone",
		"Customer Email",
		"Customer Address Company",
		"หัวบิล",
		"Meet Status",
		"Meeting Created At",
		"Meeting Service",
		"Comment",
	}
	for colIndex, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(colIndex+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// Data
	for rowIndex, data := range meetData {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIndex+2), data.MeetDateFormat) //แก้formatวันที่
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIndex+2), data.MeetTime)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIndex+2), data.MeetERPCode)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIndex+2), data.CompanyName)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", rowIndex+2), data.Block)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", rowIndex+2), data.MeetSumGrade)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", rowIndex+2), data.MeetingChannel)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", rowIndex+2), data.CustomerConference)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", rowIndex+2), data.CustomerOtherDescription)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", rowIndex+2), data.TypeOfBusiness)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", rowIndex+2), strings.Join(data.CustomerCLC, ", "))
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", rowIndex+2), data.PresaleCLCName)
		f.SetCellValue(sheetName, fmt.Sprintf("M%d", rowIndex+2), strings.Join(data.CustomerVisit, ", "))
		f.SetCellValue(sheetName, fmt.Sprintf("N%d", rowIndex+2), data.PresaleVisitName)
		f.SetCellValue(sheetName, fmt.Sprintf("O%d", rowIndex+2), data.CustomerOnsiteOther)
		f.SetCellValue(sheetName, fmt.Sprintf("P%d", rowIndex+2), strings.Join(data.CustomerPLC, ", "))
		f.SetCellValue(sheetName, fmt.Sprintf("Q%d", rowIndex+2), data.PresalePLCName)
		f.SetCellValue(sheetName, fmt.Sprintf("R%d", rowIndex+2), data.PartnerType)
		f.SetCellValue(sheetName, fmt.Sprintf("S%d", rowIndex+2), data.SaleName)
		f.SetCellValue(sheetName, fmt.Sprintf("T%d", rowIndex+2), data.BlockTeamSale)
		f.SetCellValue(sheetName, fmt.Sprintf("U%d", rowIndex+2), data.CustomerName)
		f.SetCellValue(sheetName, fmt.Sprintf("V%d", rowIndex+2), data.CustomerPosition)
		f.SetCellValue(sheetName, fmt.Sprintf("W%d", rowIndex+2), data.CustomerPhone)
		f.SetCellValue(sheetName, fmt.Sprintf("X%d", rowIndex+2), data.CustomerEmail)
		f.SetCellValue(sheetName, fmt.Sprintf("Y%d", rowIndex+2), data.FullAddress)
		f.SetCellValue(sheetName, fmt.Sprintf("Z%d", rowIndex+2), data.BillHeader)
		f.SetCellValue(sheetName, fmt.Sprintf("AA%d", rowIndex+2), data.MeetStatus)
		f.SetCellValue(sheetName, fmt.Sprintf("AB%d", rowIndex+2), data.MeetingCreatedAtFormat) //แก้formatวันที่
		f.SetCellValue(sheetName, fmt.Sprintf("AC%d", rowIndex+2), strings.Join(data.MeetingService, ", "))
		f.SetCellValue(sheetName, fmt.Sprintf("AD%d", rowIndex+2), data.Comment)
	}

	AutoAdjustColumnWidth(f, sheetName, 1, 30)

	buf := new(bytes.Buffer)
	if err := f.Write(buf); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil

}

func AutoAdjustColumnWidth(f *excelize.File, sheetName string, startRow int, totalColumns int) {
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return
	}
	endRow := len(rows)

	for col := 1; col <= totalColumns; col++ {
		colLetter := columnNumberToLetter(col)
		colWidth := 0

		header, _ := f.GetCellValue(sheetName, fmt.Sprintf("%s%d", colLetter, startRow))
		header = strings.TrimSpace(header)
		colWidth = utf8.RuneCountInString(header)

		for row := startRow + 1; row <= endRow; row++ {
			cell, _ := f.GetCellValue(sheetName, fmt.Sprintf("%s%d", colLetter, row))
			cell = strings.TrimSpace(cell)
			if utf8.RuneCountInString(cell) > colWidth {
				colWidth = utf8.RuneCountInString(cell)
			}
		}

		if colWidth < 10 {
			colWidth = 10
		} else if colWidth > 80 {
			colWidth = 80
		}

		f.SetColWidth(sheetName, colLetter, colLetter, float64(colWidth+2))
	}
}

func columnNumberToLetter(colNum int) string {
	// การคำนวณให้รองรับหลายคอลัมน์ (เช่น A, B, ..., Z, AA, AB, ...)
	var colStr string
	for colNum > 0 {
		colNum-- // ลดลง 1 เพื่อให้ทำงานกับการคำนวณ
		colStr = string(rune('A'+colNum%26)) + colStr
		colNum /= 26
	}
	return colStr
}
