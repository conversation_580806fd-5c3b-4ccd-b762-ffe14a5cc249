package model

type MeetingRawData struct {
	MeetID                   int      `bson:"_id"`
	CustomerID               string   `bson:"cus_id"`
	MeetDate                 string   `bson:"add_meet_date"`
	MeetDateFormat           string   `bson:"format_date"`
	MeetTime                 string   `bson:"add_meet_time"`
	MeetERPCode              string   `bson:"meet_erp_code"`
	CompanyName              string   `bson:"company_name_th"`
	Block                    string   `bson:"meet_block_name"`
	MeetSumGrade             string   `bson:"meet_sum_grade"`
	MeetingChannel           string   `bson:"site"`
	CustomerConference       string   `bson:"cus_conference"`
	CustomerOtherDescription string   `bson:"cus_other_desc"`
	TypeOfBusiness           string   `bson:"type_of_business_id"`
	CustomerCLC              []string `bson:"cus_clc"`
	PresaleCLCName           string   `bson:"ps_clc_name"`
	CustomerVisit            []string `bson:"cus_visit"`
	PresaleVisitName         string   `bson:"ps_visit_name"`
	CustomerOnsiteOther      string   `bson:"cus_on_site_other"`
	CustomerPLC              []string `bson:"cus_plc"`
	PresalePLCName           string   `bson:"ps_plc_name"`
	SaleName                 string   `bson:"sale_name_th"`
	BlockTeamSale            string   `bson:"meet_department_sale"`
	CustomerName             string   `bson:"cus_name"`
	CustomerPosition         string   `bson:"cus_position"`
	CustomerPhone            string   `bson:"cus_phone"`
	CustomerEmail            string   `bson:"cus_email"`
	FullAddress              string   `bson:"full_address"`
	CustomerAddressCompany   string   `bson:"cus_address_company"`
	CustomerDistricts        string   `bson:"cus_districts"`
	CustomerAmphurs          string   `bson:"cus_amphurs"`
	CustomerProvinces        string   `bson:"cus_provinces"`
	CustomerPost             string   `bson:"cus_post"`
	PartnerType              string   `bson:"partner_type"`
	BillHeader               string   `bson:"private_customer_transform_businesses"`
	MeetStatus               string   `bson:"meet_status"`
	MeetingCreatedAt         string   `bson:"meet_create_at"`
	MeetingCreatedAtFormat   string   `bson:"meet_create_at_format"`
	MeetingService           []string `bson:"meeting_service"`
	Status                   string   `bson:"status"`
	Comment                  string   `bson:"comment"`
	SaleMOM                  string   `bson:"sale_mom_desc"`
	PreSaleMOM               string   `bson:"presale_mom_desc"`
	ServiceName              []string `bson:"service_name"`
	StatusService            []string `bson:"status_service"`
	TaxId                    string   `bson:"tax_id"`
	PartnerERP               string   `bson:"partner_erp"`
	ServiceERP               string   `bson:"service_erp"`
	PartnerCRM               string   `bson:"partner_crm"`
	ServiceCRM               string   `bson:"service_crm"`
	PartnerHRM               string   `bson:"partner_hrm"`
	ServiceHRM               string   `bson:"service_hrm"`
	PartnerSupplyChain       string   `bson:"partner_supply_chain"`
	ServiceSupplyChain       string   `bson:"service_supply_chain"`
	PartnerWorkflow          string   `bson:"partner_workflow"`
	ServiceWorkflow          string   `bson:"service_workflow"`
	PartnerECommerceWeb      string   `bson:"partner_e_commerce_web"`
	ServiceECommerceWeb      string   `bson:"service_e_commerce_web"`
}
