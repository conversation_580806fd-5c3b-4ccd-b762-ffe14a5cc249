package store

import (
	"context"
	"net/http"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v3/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v3/model"
	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const MeetingCollectionName = "meeting"
const MetaDataCollectionName = "meeting_metadata"
const MeetingJoinCollectionName = "meeting_join"

type MeetingStoreImpl struct {
	meetingCollection     *mongo.Collection
	metaDataCollection    *mongo.Collection
	meetingJoinCollection *mongo.Collection
}

func NewMeetingStore(db *mongo.Database) MeetingStore {
	return &MeetingStoreImpl{
		meetingCollection:     db.Collection(MeetingCollectionName),
		metaDataCollection:    db.Collection(MetaDataCollectionName),
		meetingJoinCollection: db.Collection(MeetingJoinCollectionName),
	}
}

func (s *MeetingStoreImpl) FetchMeetingData(ctx context.Context, body dto.MeetingRequest) ([]*model.MeetingRawData, error) {
	meetData := make([]*model.MeetingRawData, 0)

	matchStage := generateMatchStage(body)

	groupStage := bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id":                  "$meet_id",
				"cus_id":               bson.M{"$first": "$cus_id"},
				"add_meet_date":        bson.M{"$first": "$add_meet_date"},
				"add_meet_time":        bson.M{"$first": "$add_meet_time"},
				"meet_erp_code":        bson.M{"$first": "$meet_erp_code"},
				"company_name_th":      bson.M{"$first": "$company_name_th"},
				"meet_block_name":      bson.M{"$first": "$meet_block_name"},
				"meet_sum_grade":       bson.M{"$first": "$meet_sum_grade"},
				"cus_conference":       bson.M{"$first": "$cus_conference"},
				"cus_other_desc":       bson.M{"$first": "$cus_other_desc"},
				"type_of_business_id":  bson.M{"$first": "$type_of_business_id"},
				"cus_clc":              bson.M{"$first": "$cus_clc"},
				"ps_clc_name":          bson.M{"$first": "$ps_clc_name"},
				"cus_visit":            bson.M{"$first": "$cus_visit"},
				"ps_visit_name":        bson.M{"$first": "$ps_visit_name"},
				"cus_on_site_other":    bson.M{"$first": "$cus_on_site_other"},
				"cus_plc":              bson.M{"$first": "$cus_plc"},
				"ps_plc_name":          bson.M{"$first": "$ps_plc_name"},
				"sale_name_th":         bson.M{"$first": "$sale_name_th"},
				"partner_type":         bson.M{"$first": "$partner_type"},
				"meet_department_sale": bson.M{"$first": "$meet_department_sale"},
				"cus_name":             bson.M{"$first": "$cus_name"},
				"cus_position":         bson.M{"$first": "$cus_position"},
				"cus_phone":            bson.M{"$first": "$cus_phone"},
				"cus_email":            bson.M{"$first": "$cus_email"},
				"cus_address_company":  bson.M{"$first": "$cus_address_company"},
				"cus_districts":        bson.M{"$first": "$cus_districts"},
				"cus_amphurs":          bson.M{"$first": "$cus_amphurs"},
				"cus_provinces":        bson.M{"$first": "$cus_provinces"},
				"cus_post":             bson.M{"$first": "$cus_post"},
				"full_address": bson.M{
					"$first": bson.M{
						"$concat": []interface{}{
							bson.M{"$ifNull": []interface{}{bson.M{"$trim": bson.M{"input": "$cus_address_company"}}, ""}},
							" ",
							bson.M{"$ifNull": []interface{}{bson.M{"$trim": bson.M{"input": "$cus_districts"}}, ""}},
							" ",
							bson.M{"$ifNull": []interface{}{bson.M{"$trim": bson.M{"input": "$cus_amphurs"}}, ""}},
							" ",
							bson.M{"$ifNull": []interface{}{bson.M{"$trim": bson.M{"input": "$cus_provinces"}}, ""}},
							" ",
							bson.M{"$ifNull": []interface{}{bson.M{"$trim": bson.M{"input": "$cus_post"}}, ""}},
						},
					},
				},
				"private_customer_transform_businesses": bson.M{"$first": "$private_customer_transform_businesses"},
				"meet_status":                           bson.M{"$first": "$meet_status"},
				"meet_create_at":                        bson.M{"$first": "$meet_create_at"},
				"site":                                  bson.M{"$first": "$site"},
				"comment":                               bson.M{"$first": "$comment"},
				"meeting_service":                       bson.M{"$first": "$meeting_service"},
				"status":                                bson.M{"$first": "$status"},
				"sale_mom_desc":                         bson.M{"$first": "$sale_mom_desc"},
				"presale_mom_desc":                      bson.M{"$first": "$presale_mom_desc"},
				"StageServices":                         bson.M{"$first": "$StageServices"},
				"tax_id":                                bson.M{"$first": "$tax_id"},
				"partner_erp":                           bson.M{"$first": "$partner_erp"},
				"service_erp":                           bson.M{"$first": "$service_erp"},
				"partner_crm":                           bson.M{"$first": "$partner_crm"},
				"service_crm":                           bson.M{"$first": "$service_crm"},
				"partner_hrm":                           bson.M{"$first": "$partner_hrm"},
				"service_hrm":                           bson.M{"$first": "$service_hrm"},
				"partner_supply_chain":                  bson.M{"$first": "$partner_supply_chain"},
				"service_supply_chain":                  bson.M{"$first": "$service_supply_chain"},
				"partner_workflow":                      bson.M{"$first": "$partner_workflow"},
				"service_workflow":                      bson.M{"$first": "$service_workflow"},
				"partner_e_commerce_web":                bson.M{"$first": "$partner_e_commerce_web"},
				"service_e_commerce_web":                bson.M{"$first": "$service_e_commerce_web"},
			},
		},
	}

	projectStage := bson.D{
		{
			Key: "$project",
			Value: bson.M{
				"meet_id":         "$_id",
				"cus_id":          1,
				"add_meet_date":   1,
				"add_meet_time":   1,
				"meet_erp_code":   1,
				"company_name_th": 1,
				"meet_block_name": 1,
				"meet_sum_grade":  1,
				"cus_conference": bson.M{
					"$reduce": bson.M{
						"input": bson.A{
							"Online",
							"Onsite",
							"ที่อยู่บริษัทลูกค้า",
						},
						"initialValue": "$cus_conference",
						"in": bson.M{
							"$replaceAll": bson.M{
								"input":       "$$value",
								"find":        "$$this",
								"replacement": "",
							},
						},
					},
				},
				"cus_other_desc": bson.M{
					"$reduce": bson.M{
						"input": bson.A{
							"Service ที่เข้าไปนำเสนอคือ",
						},
						"initialValue": "$cus_other_desc",
						"in": bson.M{
							"$replaceAll": bson.M{
								"input":       "$$value",
								"find":        "$$this",
								"replacement": "",
							},
						},
					},
				},
				"type_of_business_id":                   1,
				"cus_clc":                               1,
				"ps_clc_name":                           1,
				"cus_visit":                             1,
				"ps_visit_name":                         1,
				"cus_on_site_other":                     1,
				"cus_plc":                               1,
				"ps_plc_name":                           1,
				"sale_name_th":                          1,
				"meet_department_sale":                  1,
				"cus_name":                              1,
				"cus_position":                          1,
				"cus_phone":                             1,
				"cus_email":                             1,
				"full_address":                          1,
				"cus_address_company":                   1,
				"cus_districts":                         1,
				"cus_amphurs":                           1,
				"cus_provinces":                         1,
				"cus_post":                              1,
				"private_customer_transform_businesses": 1,
				"meet_status":                           1,
				"meet_create_at":                        1,
				"partner_type":                          1,
				"status":                                1,
				"site":                                  1,
				"meeting_service":                       1,
				"comment":                               1,
				"sale_mom_desc":                         1,
				"presale_mom_desc":                      1,
				"tax_id":                                1,
				"partner_erp":                           1,
				"service_erp":                           1,
				"partner_crm":                           1,
				"service_crm":                           1,
				"partner_hrm":                           1,
				"service_hrm":                           1,
				"partner_supply_chain":                  1,
				"service_supply_chain":                  1,
				"partner_workflow":                      1,
				"service_workflow":                      1,
				"partner_e_commerce_web":                1,
				"service_e_commerce_web":                1,
				"service_name": bson.M{
					"$map": bson.M{
						"input": "$StageServices",
						"as":    "svc",
						"in":    "$$svc.service_name",
					},
				},
				"status_service": bson.M{
					"$map": bson.M{
						"input": "$StageServices",
						"as":    "svc",
						"in": bson.M{
							"$concat": bson.A{
								"$$svc.service_status",
								"-",
								"$$svc.service_status_description",
							},
						},
					},
				},
				"meet_datetime": bson.M{
					"$dateFromString": bson.M{
						"dateString": bson.M{
							"$concat": bson.A{
								"$add_meet_date", "T", "$add_meet_time",
							},
						},
						"timezone": "Asia/Bangkok",
					},
				},
				"format_date": bson.M{
					"$dateToString": bson.M{
						"format": "%d/%m/%Y",
						"date": bson.M{
							"$dateFromString": bson.M{
								"dateString": "$add_meet_date",
							},
						},
					},
				},
				"meet_create_at_format": bson.M{
					"$dateToString": bson.M{
						"format": "%d/%m/%Y",
						"date": bson.M{
							"$dateFromString": bson.M{
								"dateString": "$meet_create_at",
							},
						},
					},
				},
			},
		},
	}

	//พี่น๊อตแก้ไว้
	var sortStage bson.D
	if len(body.Sort) > 0 {
		sortFields := bson.D{}
		for field, direction := range body.Sort {
			sortKey := ""
			switch field {
			case "add_meet_date":
				sortKey = "add_meet_date"
			case "add_meet_time":
				sortKey = "add_meet_time"
			case "ps_clc_name":
				sortKey = "ps_clc_name"
			case "ps_visit_name":
				sortKey = "ps_visit_name"
			case "ps_plc_name":
				sortKey = "ps_plc_name"
			case "sale_name_th":
				sortKey = "sale_name_th"
			case "block":
				sortKey = "meet_block_name"
			case "block_team_sale":
				sortKey = "meet_department_sale"
			case "meet_status":
				sortKey = "meet_status"
			case "sum_grade":
				sortKey = "meet_sum_grade"
			case "meeting_channel":
				sortKey = "site"
			default:
				continue
			}
			sortFields = append(sortFields, bson.E{Key: sortKey, Value: direction})
			// sortFields = append(sortFields, bson.E{Key: "meet_id", Value: 1})
		}

		if len(sortFields) > 0 {
			sortFields = append(sortFields, bson.E{Key: "meet_id", Value: -1})
			sortStage = bson.D{{Key: "$sort", Value: sortFields}}
		}
	} else {
		sortStage = bson.D{{Key: "$sort", Value: bson.D{
			{Key: "meet_datetime", Value: -1},
			{Key: "meet_id", Value: -1},
		}}}
	}
	pipeline := mongo.Pipeline{}

	pipeline = append(pipeline,
		matchStage,
		groupStage,
		projectStage,
		sortStage,
	)

	if body.Page > 0 && body.Limit > 0 {
		pipeline = append(pipeline,
			bson.D{{Key: "$skip", Value: (body.Page - 1) * body.Limit}},
			bson.D{{Key: "$limit", Value: body.Limit}},
		)
	}

	cursor, err := s.meetingJoinCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	defer cursor.Close(ctx)

	if err := cursor.All(ctx, &meetData); err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}

	return meetData, nil
}

func (s *MeetingStoreImpl) CountMeetingData(ctx context.Context, body dto.MeetingRequest) (int64, error) {

	matchStage := generateMatchStage(body)

	groupStage := bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id": "$meet_id",
			},
		},
	}

	countPipeline := mongo.Pipeline{
		matchStage,
		groupStage,
		bson.D{{Key: "$count", Value: "total"}},
	}

	countCursor, err := s.meetingJoinCollection.Aggregate(ctx, countPipeline)
	if err != nil {
		return 0, apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	var countResult struct {
		Total int `bson:"total"`
	}
	if countCursor.Next(ctx) {
		if err := countCursor.Decode(&countResult); err != nil {
			return 0, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
		}
	}
	return int64(countResult.Total), nil
}

func generateMatchStage(body dto.MeetingRequest) bson.D {

	startDateStr := body.MeetStartDate
	if startDateStr == "" {
		startDateStr = "2024-01-01"
	}

	endDateStr := body.MeetEndDate

	startTimeStr := body.MeetStartTime
	if startTimeStr == "" {
		startTimeStr = "00:00:00"
	}

	endTimeStr := body.MeetEndTime
	if endTimeStr == "" {
		endTimeStr = "23:59:59"
	}

	matchCond := bson.M{}

	if endDateStr == "" {
		matchCond["add_meet_date"] = bson.M{
			"$gte": startDateStr,
		}
		matchCond["add_meet_time"] = bson.M{
			"$gte": startTimeStr,
			"$lte": endTimeStr,
		}
	} else {
		matchCond["add_meet_date"] = bson.M{
			"$gte": startDateStr,
			"$lte": endDateStr,
		}
		matchCond["add_meet_time"] = bson.M{
			"$gte": startTimeStr,
			"$lte": endTimeStr,
		}
	}

	if len(body.BlockCustomer) > 0 {
		matchCond["meet_block_name"] = bson.M{
			"$in": body.BlockCustomer,
		}
	}
	if len(body.BlockTeamSale) > 0 {
		matchCond["meet_department_sale"] = bson.M{
			"$in": body.BlockTeamSale,
		}
	}
	if len(body.CustomerCLC) > 0 {
		matchCond["cus_clc"] = bson.M{
			"$in": body.CustomerCLC,
		}
	}
	if len(body.PresaleCLCName) > 0 {
		matchCond["ps_clc_name"] = bson.M{
			"$in": body.PresaleCLCName,
		}
	}
	if len(body.PresaleVisitName) > 0 {
		matchCond["ps_visit_name"] = bson.M{
			"$in": body.PresaleVisitName,
		}
	}
	if len(body.CustomerPLC) > 0 {
		matchCond["plc"] = bson.M{
			"$in": body.CustomerPLC,
		}
	}
	if len(body.PresalePLCName) > 0 {
		matchCond["ps_plc_name"] = bson.M{
			"$in": body.PresalePLCName,
		}
	}
	if len(body.Status) > 0 {
		matchCond["status"] = bson.M{
			"$in": body.Status,
		}
	}
	if len(body.MeetingService) > 0 {
		matchCond["meeting_service"] = bson.M{
			"$in": body.MeetingService,
		}
	}

	return bson.D{{Key: "$match", Value: matchCond}}
}
