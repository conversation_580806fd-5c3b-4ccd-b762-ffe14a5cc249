package store

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/model"
)

type MeetingStore interface {
	FetchMeetingData(ctx context.Context, body dto.MeetingRequest) ([]*model.MeetingRawData, error)
	CountMeetingData(ctx context.Context, body dto.MeetingRequest) (int64, error)
	UpsertMeetingService(ctx context.Context, body dto.MeetingServiceRequest) error
	GetMeetingServices(ctx context.Context, meetId int) ([]string, error)
	UpsertMeetingLog(ctx context.Context, body dto.MeetingLogData) error
	FindMeetingService(ctx context.Context, meetId int) ([]string, error)
	GetMeetingLog(ctx context.Context, body dto.MeetingLogRequest) ([]*model.MeetingLogData, error)
	CountMeetingLogData(ctx context.Context, body dto.MeetingLogRequest) (int64, error)
}

type CommentStore interface {
	UpsertComment(ctx context.Context, comment string, meetId int) error
	UpsertCommentLog(ctx context.Context, comment *model.CommentLog) error
	GetComment(ctx context.Context, meetId int) (string, error)
	FindComment(ctx context.Context, meetId int) (string, error)

	GetListComment(ctx context.Context, meetId int) ([]*model.CommentLog, error)
}

type StatusStore interface {
	UpsertStatus(ctx context.Context, status string, meetId int) error
	UpsertStatusLog(ctx context.Context, comment *model.StatusLog) error
	GetStatus(ctx context.Context, meetId int) (string, error)
	FindStatus(ctx context.Context, meetId int) (string, error)

	GetListStatus(ctx context.Context, meetId int) ([]*model.StatusLog, error)
}

type FilterStore interface {
	GetAllBlockCustomer(ctx context.Context) ([]string, error)
	GetAllBlockTeamSale(ctx context.Context) ([]string, error)
	GetAllPresaleClcName(ctx context.Context) ([]string, error)
	GetAllPresaleVisitName(ctx context.Context) ([]string, error)
	GetAllPresalePlcName(ctx context.Context) ([]string, error)
	GetAllCustomerPlc(ctx context.Context) ([]string, error)
	GetAllCustomerClc(ctx context.Context) ([]string, error)
}
