package store

import (
	"context"
	"errors"
	"net/http"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/model"
	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const StatusLogCollectionName = "status_log"

type StatusStoreImpl struct {
	statusLogCollection   *mongo.Collection
	meetingCollection     *mongo.Collection
	metaDataCollection    *mongo.Collection
	meetingJoinCollection *mongo.Collection
}

func NewStatusStore(db *mongo.Database) StatusStore {
	return &StatusStoreImpl{
		statusLogCollection:   db.Collection(StatusLogCollectionName),
		meetingCollection:     db.Collection(MeetingCollectionName),
		metaDataCollection:    db.Collection(MetaDataCollectionName),
		meetingJoinCollection: db.Collection(MeetingJoinCollectionName),
	}
}

func (s *StatusStoreImpl) UpsertStatus(ctx context.Context, status string, meetId int) error {

	err := s.meetingJoinCollection.FindOne(ctx, bson.M{"meet_id": meetId}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return apperrors.ErrNotFound
		}
		return apperrors.NewAppError(http.StatusInternalServerError, "error checking meet_id", err)
	}

	filterMeet := bson.M{"meet_id": meetId}
	update := bson.M{
		"$set": bson.M{
			"status": status,
		},
	}
	opts := options.Update().SetUpsert(true)

	_, err = s.meetingJoinCollection.UpdateOne(ctx, filterMeet, update)
	if err != nil {
		return apperrors.NewAppError(http.StatusInternalServerError, "updating failed for meeting-join collection", err)
	}

	_, err = s.metaDataCollection.UpdateOne(ctx, filterMeet, update, opts)
	if err != nil {
		return apperrors.NewAppError(http.StatusInternalServerError, "updating failed for metadata collection", err)
	}

	return nil
}

func (s *StatusStoreImpl) UpsertStatusLog(ctx context.Context, statusLog *model.StatusLog) error {

	if statusLog.CreatedAt.IsZero() {
		statusLog.CreatedAt = time.Now()
	}

	var existingStatus struct {
		Status string `bson:"status"`
	}

	filter := bson.M{"meet_id": statusLog.MeetId}

	err := s.meetingJoinCollection.FindOne(ctx, filter).Decode(&existingStatus)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return apperrors.ErrNotFound
		} else {
			return apperrors.NewAppError(http.StatusInternalServerError, "error finding status", err)
		}
	}

	statusLog.OldStatus = existingStatus.Status
	_, insertErr := s.statusLogCollection.InsertOne(ctx, statusLog)
	if insertErr != nil {
		return apperrors.NewAppError(http.StatusInternalServerError, "error inserting status log", err)
	}
	return nil

}

func (s *StatusStoreImpl) GetStatus(ctx context.Context, meetId int) (string, error) {
	type StatusData struct {
		Status string `bson:"status"`
	}

	matchStage := bson.D{
		{
			Key: "$match",
			Value: bson.M{
				"meet_id": meetId,
			},
		},
	}

	groupStage := bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id":    "$meet_id",
				"status": bson.M{"$first": "$status"},
			},
		},
	}

	projectStage := bson.D{
		{
			Key: "$project",
			Value: bson.M{
				"_id":    0,
				"status": 1,
			},
		},
	}

	pipeline := mongo.Pipeline{matchStage, groupStage, projectStage}

	cursor, err := s.meetingJoinCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return "", apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	defer cursor.Close(ctx)

	var status []StatusData
	if err := cursor.All(ctx, &status); err != nil {
		return "", apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}

	if len(status) == 0 {
		return "", apperrors.ErrNotFound
	}

	return status[0].Status, nil
}

func (s *StatusStoreImpl) GetListStatus(ctx context.Context, meetId int) ([]*model.StatusLog, error) {

	filter := bson.M{"meet_id": bson.M{"$eq": meetId}}
	cursor, err := s.statusLogCollection.Find(ctx, filter)
	if err != nil {
		return []*model.StatusLog{}, apperrors.NewAppError(http.StatusInternalServerError, "failed to find status", err)
	}
	defer cursor.Close(ctx)

	var status []*model.StatusLog
	if err := cursor.All(ctx, &status); err != nil {
		return []*model.StatusLog{}, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}

	if len(status) == 0 {
		return []*model.StatusLog{}, apperrors.ErrNotFound
	}

	return status, nil
}

func (s *StatusStoreImpl) FindStatus(ctx context.Context, meetId int) (string, error) {
	type StatusData struct {
		Status string `bson:"status"`
	}

	var result StatusData

	err := s.meetingJoinCollection.
		FindOne(ctx, bson.M{"meet_id": meetId}).
		Decode(&result)

	if err != nil {
		return "", apperrors.NewAppError(http.StatusInternalServerError, "finding status failed", err)
	}

	return result.Status, nil
}
