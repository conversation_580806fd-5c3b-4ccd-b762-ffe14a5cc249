package store

import (
	"context"
	"errors"
	"net/http"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/model"
	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const CommentLogCollectionName = "comment_log"

type CommentStoreImpl struct {
	commentLogCollection  *mongo.Collection
	meetingCollection     *mongo.Collection
	metaDataCollection    *mongo.Collection
	meetingJoinCollection *mongo.Collection
}

func NewCommentStore(db *mongo.Database) CommentStore {
	return &CommentStoreImpl{
		commentLogCollection:  db.Collection(CommentLogCollectionName),
		meetingCollection:     db.Collection(MeetingCollectionName),
		metaDataCollection:    db.Collection(MetaDataCollectionName),
		meetingJoinCollection: db.Collection(MeetingJoinCollectionName),
	}
}

func (s *CommentStoreImpl) UpsertComment(ctx context.Context, comment string, meetId int) error {

	err := s.meetingJoinCollection.FindOne(ctx, bson.M{"meet_id": meetId}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return apperrors.ErrNotFound
		}
		return apperrors.NewAppError(http.StatusInternalServerError, "error checking meet_id", err)
	}

	filterMeet := bson.M{"meet_id": meetId}
	update := bson.M{
		"$set": bson.M{
			"comment": comment,
		},
	}
	opts := options.Update().SetUpsert(true)

	_, err = s.meetingJoinCollection.UpdateOne(ctx, filterMeet, update)
	if err != nil {
		return apperrors.NewAppError(http.StatusInternalServerError, "updating failed for meeting-join collection", err)
	}

	_, err = s.metaDataCollection.UpdateOne(ctx, filterMeet, update, opts)
	if err != nil {
		return apperrors.NewAppError(http.StatusInternalServerError, "updating failed for metadata collection", err)
	}

	return nil
}

func (s *CommentStoreImpl) UpsertCommentLog(ctx context.Context, comment *model.CommentLog) error {

	if comment.CreatedAt.IsZero() {
		comment.CreatedAt = time.Now()
	}

	var existingComment struct {
		Comment string `bson:"comment"`
	}

	filter := bson.M{"meet_id": comment.MeetId}

	err := s.meetingJoinCollection.FindOne(ctx, filter).Decode(&existingComment)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return apperrors.ErrNotFound
		} else {
			return apperrors.NewAppError(http.StatusInternalServerError, "error finding comment", err)
		}
	}

	comment.OldComment = existingComment.Comment
	_, insertErr := s.commentLogCollection.InsertOne(ctx, comment)
	if insertErr != nil {
		return apperrors.NewAppError(http.StatusInternalServerError, "error inserting comment log", insertErr)
	}
	return nil

}

func (s *CommentStoreImpl) GetComment(ctx context.Context, meetId int) (string, error) {
	type CommentData struct {
		Comment string `bson:"comment"`
	}

	matchStage := bson.D{
		{
			Key: "$match",
			Value: bson.M{
				"meet_id": meetId,
			},
		},
	}

	groupStage := bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id":     "$meet_id",
				"comment": bson.M{"$first": "$comment"},
			},
		},
	}

	projectStage := bson.D{
		{
			Key: "$project",
			Value: bson.M{
				"_id":     0,
				"comment": 1,
			},
		},
	}

	pipeline := mongo.Pipeline{matchStage, groupStage, projectStage}

	cursor, err := s.meetingJoinCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return "", apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	defer cursor.Close(ctx)

	var comment []CommentData
	if err := cursor.All(ctx, &comment); err != nil {
		return "", apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}
	if len(comment) == 0 {
		return "", apperrors.ErrNotFound
	}

	return comment[0].Comment, nil
}

func (s *CommentStoreImpl) GetListComment(ctx context.Context, meetId int) ([]*model.CommentLog, error) {

	filter := bson.M{"meet_id": bson.M{"$eq": meetId}}
	cursor, err := s.commentLogCollection.Find(ctx, filter)
	if err != nil {
		return []*model.CommentLog{}, apperrors.NewAppError(http.StatusInternalServerError, "failed to find comments", err)
	}
	defer cursor.Close(ctx)

	var comment []*model.CommentLog
	if err := cursor.All(ctx, &comment); err != nil {
		return []*model.CommentLog{}, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}

	if len(comment) == 0 {
		return []*model.CommentLog{}, apperrors.ErrNotFound
	}

	return comment, nil
}

func (s *CommentStoreImpl) FindComment(ctx context.Context, meetId int) (string, error) {
	type CommentData struct {
		Comment string `bson:"comment"`
	}

	var result CommentData

	err := s.meetingJoinCollection.
		FindOne(ctx, bson.M{"meet_id": meetId}).
		Decode(&result)

	if err != nil {
		return "", apperrors.NewAppError(http.StatusInternalServerError, "finding comment failed", err)
	}

	return result.Comment, nil
}
