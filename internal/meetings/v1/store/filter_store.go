package store

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type FilterStoreImpl struct {
	meetingJoinCollection *mongo.Collection
}

func NewFilterStore(db *mongo.Database) FilterStore {
	return &FilterStoreImpl{
		meetingJoinCollection: db.Collection(MeetingJoinCollectionName),
	}
}

func (s *FilterStoreImpl) GetAllBlockCustomer(ctx context.Context) ([]string, error) {
	distinctValues, err := s.meetingJoinCollection.Distinct(ctx, "meet_block_name", bson.M{})
	if err != nil {
		return nil, err
	}

	result := make([]string, 0)
	for _, v := range distinctValues {
		if str, ok := v.(string); ok {
			result = append(result, str)
		}
	}

	return result, nil
}

func (s *FilterStoreImpl) GetAllBlockTeamSale(ctx context.Context) ([]string, error) {
	distinctValues, err := s.meetingJoinCollection.Distinct(ctx, "meet_department_sale", bson.M{})
	if err != nil {
		return nil, err
	}

	result := make([]string, 0)
	for _, v := range distinctValues {
		if str, ok := v.(string); ok {
			if str != "" {
				result = append(result, str)
			}
		}
	}

	return result, nil
}

func (s *FilterStoreImpl) GetAllPresaleClcName(ctx context.Context) ([]string, error) {
	distinctValues, err := s.meetingJoinCollection.Distinct(ctx, "ps_clc_name", bson.M{})
	if err != nil {
		return nil, err
	}

	result := make([]string, 0)
	for _, v := range distinctValues {
		if str, ok := v.(string); ok {
			if str != "" {
				result = append(result, str)
			}
		}
	}

	return result, nil
}

func (s *FilterStoreImpl) GetAllPresaleVisitName(ctx context.Context) ([]string, error) {
	distinctValues, err := s.meetingJoinCollection.Distinct(ctx, "ps_visit_name", bson.M{})
	if err != nil {
		return nil, err
	}

	result := make([]string, 0)
	for _, v := range distinctValues {
		if str, ok := v.(string); ok {
			if str != "" {
				result = append(result, str)
			}
		}
	}

	return result, nil
}

func (s *FilterStoreImpl) GetAllPresalePlcName(ctx context.Context) ([]string, error) {
	distinctValues, err := s.meetingJoinCollection.Distinct(ctx, "ps_plc_name", bson.M{})
	if err != nil {
		return nil, err
	}

	result := make([]string, 0)
	for _, v := range distinctValues {
		if str, ok := v.(string); ok {
			if str != "" {
				result = append(result, str)
			}
		}
	}

	return result, nil
}

func (s *FilterStoreImpl) GetAllCustomerPlc(ctx context.Context) ([]string, error) {
	distinctValues, err := s.meetingJoinCollection.Distinct(ctx, "cus_plc", bson.M{})
	if err != nil {
		return nil, err
	}

	result := make([]string, 0)
	for _, v := range distinctValues {
		if str, ok := v.(string); ok {
			if str != "" {
				result = append(result, str)
			}
		}
	}

	return result, nil
}

func (s *FilterStoreImpl) GetAllCustomerClc(ctx context.Context) ([]string, error) {
	distinctValues, err := s.meetingJoinCollection.Distinct(ctx, "cus_clc", bson.M{})
	if err != nil {
		return nil, err
	}

	result := make([]string, 0)
	for _, v := range distinctValues {
		if str, ok := v.(string); ok {
			if str != "" {
				result = append(result, str)
			}
		}
	}

	return result, nil
}
