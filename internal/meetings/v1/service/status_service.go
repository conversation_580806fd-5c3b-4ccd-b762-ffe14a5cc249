package service

import (
	"context"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/model"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/store"
)

type StatusServiceImpl struct {
	statusStore  store.StatusStore
	meetingStore store.MeetingStore
}

func NewStatusService(
	statusStore store.StatusStore,
	meetingStore store.MeetingStore,
) StatusService {
	return &StatusServiceImpl{
		statusStore:  statusStore,
		meetingStore: meetingStore,
	}
}

func (s *StatusServiceImpl) InsertStatus(ctx context.Context, accountID string, fullName string, status string, meetId int) error {

	statusLog := &model.StatusLog{
		AccountId: accountID,
		MeetId:    meetId,
		NewStatus: status,
	}

	oldStatus, err := s.statusStore.FindStatus(ctx, meetId)
	if err != nil {
		return err
	}

	err = s.statusStore.UpsertStatusLog(ctx, statusLog)
	if err != nil {
		return err
	}

	err = s.statusStore.UpsertStatus(ctx, status, meetId)
	if err != nil {
		return err
	}

	var log dto.MeetingLogData

	if oldStatus == "-" {
		log = dto.MeetingLogData{
			MeetId:    meetId,
			Timestamp: time.Now(),
			Action:    "เพิ่ม Status เป็น " + status,
			Actor:     fullName,
		}
	} else {
		log = dto.MeetingLogData{
			MeetId:    meetId,
			Timestamp: time.Now(),
			Action:    "แก้ไข Status จาก " + oldStatus + " เป็น " + status,
			Actor:     fullName,
		}
	}

	err = s.meetingStore.UpsertMeetingLog(ctx, log)
	if err != nil {
		return err
	}

	return nil
}

func (s *StatusServiceImpl) GetStatus(ctx context.Context, meetId int) (*dto.StatusResponse, error) {

	status, err := s.statusStore.GetStatus(ctx, meetId)
	if err != nil {
		return nil, err
	}

	return &dto.StatusResponse{
		Success: true,
		Status:  status,
	}, nil
}

func (s *StatusServiceImpl) GetListStatus(ctx context.Context, meetId int) (*dto.StatusLogResponse, error) {

	status, err := s.statusStore.GetListStatus(ctx, meetId)
	if err != nil {
		return &dto.StatusLogResponse{}, err
	}

	statusDTOs := make([]dto.StatusLogData, len(status))
	for i, data := range status {
		statusDTOs[i] = *dto.ToStatusLogData(data)
	}

	return &dto.StatusLogResponse{
		Success: true,
		Data:    statusDTOs,
	}, nil
}
