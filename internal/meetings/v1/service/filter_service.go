package service

import (
	"context"

	apperror "git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/store"
)

type FilterServiceImpl struct {
	filterStore store.FilterStore
}

func NewFilterService(
	filterStore store.FilterStore,
) FilterService {
	return &FilterServiceImpl{
		filterStore: filterStore,
	}
}

func (s *FilterServiceImpl) ListBlockCustomer(ctx context.Context) ([]string, error) {
	values, err := s.filterStore.GetAllBlockCustomer(ctx)
	if err != nil {
		return values, apperror.NewInternalServerError("Error while retrieve database")
	}

	return values, nil
}

func (s *FilterServiceImpl) ListBlockTeamSale(ctx context.Context) ([]string, error) {
	values, err := s.filterStore.GetAllBlockTeamSale(ctx)
	if err != nil {
		return values, apperror.NewInternalServerError("Error while retrieve database")
	}

	return values, nil
}

func (s *FilterServiceImpl) ListPresaleClcName(ctx context.Context) ([]string, error) {
	values, err := s.filterStore.GetAllPresaleClcName(ctx)
	if err != nil {
		return values, apperror.NewInternalServerError("Error while retrieve database")
	}

	return values, nil
}

func (s *FilterServiceImpl) ListPresaleVisitName(ctx context.Context) ([]string, error) {
	values, err := s.filterStore.GetAllPresaleVisitName(ctx)
	if err != nil {
		return values, apperror.NewInternalServerError("Error while retrieve database")
	}

	return values, nil
}

func (s *FilterServiceImpl) ListPresalePlcName(ctx context.Context) ([]string, error) {
	values, err := s.filterStore.GetAllPresalePlcName(ctx)
	if err != nil {
		return values, apperror.NewInternalServerError("Error while retrieve database")
	}

	return values, nil
}

func (s *FilterServiceImpl) ListCustomerPlc(ctx context.Context) ([]string, error) {
	values, err := s.filterStore.GetAllCustomerPlc(ctx)
	if err != nil {
		return values, apperror.NewInternalServerError("Error while retrieve database")
	}

	return values, nil
}

func (s *FilterServiceImpl) ListCustomerClc(ctx context.Context) ([]string, error) {
	values, err := s.filterStore.GetAllCustomerClc(ctx)
	if err != nil {
		return values, apperror.NewInternalServerError("Error while retrieve database")
	}

	return values, nil
}
