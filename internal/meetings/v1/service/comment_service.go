package service

import (
	"context"
	"fmt"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/model"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/store"
)

type CommentServiceImpl struct {
	commentStore store.CommentStore
	meetingStore store.MeetingStore
}

func NewCommentService(
	commentStore store.CommentStore,
	meetingStore store.MeetingStore,
) CommentService {
	return &CommentServiceImpl{
		commentStore: commentStore,
		meetingStore: meetingStore,
	}
}

func (s *CommentServiceImpl) InsertComment(ctx context.Context, accountID string, fullName string, comment string, meetId int) error {

	commentLog := &model.CommentLog{
		AccountId:  accountID,
		MeetId:     meetId,
		NewComment: comment,
	}

	oldCommnet, err := s.commentStore.FindComment(ctx, meetId)
	if err != nil {
		return err
	}

	err = s.commentStore.UpsertCommentLog(ctx, commentLog)
	if err != nil {
		return err
	}

	err = s.commentStore.UpsertComment(ctx, comment, meetId)
	if err != nil {
		return err
	}

	var log dto.MeetingLogData

	if oldCommnet == "-" {
		log = dto.MeetingLogData{
			MeetId:    meetId,
			Timestamp: time.Now(),
			Action:    fmt.Sprintf("เพิ่ม Comments เป็น (%s)", comment),
			Actor:     fullName,
		}
	} else {
		log = dto.MeetingLogData{
			MeetId:    meetId,
			Timestamp: time.Now(),
			Action:    fmt.Sprintf("แก้ไข Comments จาก (%s) เป็น (%s)", oldCommnet, comment),
			Actor:     fullName,
		}
	}

	err = s.meetingStore.UpsertMeetingLog(ctx, log)
	if err != nil {
		return err
	}

	return nil
}

func (s *CommentServiceImpl) GetComment(ctx context.Context, meetId int) (*dto.CommentResponse, error) {

	comment, err := s.commentStore.GetComment(ctx, meetId)
	if err != nil {
		return &dto.CommentResponse{}, err
	}

	return &dto.CommentResponse{
		Success: true,
		Comment: comment,
	}, nil
}

func (s *CommentServiceImpl) GetListComment(ctx context.Context, meetId int) (*dto.CommentLogResponse, error) {

	comment, err := s.commentStore.GetListComment(ctx, meetId)
	if err != nil {
		return &dto.CommentLogResponse{}, err
	}

	commentDTOs := make([]dto.CommentLogData, len(comment))
	for i, data := range comment {
		commentDTOs[i] = *dto.ToCommentLogData(data)
	}

	return &dto.CommentLogResponse{
		Success: true,
		Data:    commentDTOs,
	}, nil
}
