package service

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/dto"
)

type MeetingService interface {
	FetchMeetingData(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingResponse, error)
	UpsertMeetingService(ctx context.Context, fullName string, body dto.MeetingServiceRequest) error
	GetMeetingService(ctx context.Context, meetId int) (*dto.MeetingServiceResponse, error)
	ExportDataToExcel(ctx context.Context, body dto.MeetingRequest, filename string) ([]byte, error)
	GetMeetingLog(ctx context.Context, body dto.MeetingLogRequest) (*dto.MeetingLogResponseV2, error)
}

type CommentService interface {
	InsertComment(ctx context.Context, accountID string, fullName string, comment string, meetId int) error
	GetComment(ctx context.Context, meetId int) (*dto.CommentResponse, error)

	GetListComment(ctx context.Context, meetId int) (*dto.CommentLogResponse, error)
}

type StatusService interface {
	InsertStatus(ctx context.Context, accountID string, fullName string, status string, meetId int) error
	GetStatus(ctx context.Context, meetId int) (*dto.StatusResponse, error)

	GetListStatus(ctx context.Context, meetId int) (*dto.StatusLogResponse, error)
}

type FilterService interface {
	ListBlockCustomer(ctx context.Context) ([]string, error)
	ListBlockTeamSale(ctx context.Context) ([]string, error)
	ListPresaleClcName(ctx context.Context) ([]string, error)
	ListPresaleVisitName(ctx context.Context) ([]string, error)
	ListPresalePlcName(ctx context.Context) ([]string, error)
	ListCustomerPlc(ctx context.Context) ([]string, error)
	ListCustomerClc(ctx context.Context) ([]string, error)
}
