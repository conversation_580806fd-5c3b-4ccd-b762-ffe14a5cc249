package dto

import (
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/model"
)

type StatusResponse struct {
	Success bool   `json:"success"`
	Status  string `json:"status"`
}

type StatusLogResponse struct {
	Success bool            `json:"success"`
	Data    []StatusLogData `json:"data"`
}
type StatusLogData struct {
	AccountId string    `json:"account_id"`
	MeetId    int       `json:"meet_id"`
	NewStatus string    `json:"new_status"`
	OldStatus string    `json:"old_status"`
	CreatedAt time.Time `json:"created_at"`
}

func ToStatusLogData(data *model.StatusLog) *StatusLogData {
	return &StatusLogData{
		AccountId: data.AccountId,
		MeetId:    data.MeetId,
		NewStatus: data.NewStatus,
		OldStatus: data.OldStatus,
		CreatedAt: data.CreatedAt,
	}
}
