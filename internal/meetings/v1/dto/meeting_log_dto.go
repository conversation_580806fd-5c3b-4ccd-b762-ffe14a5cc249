package dto

import (
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/model"
)

type MeetingLogRequest struct {
	MeetId int `json:"meet_id"`
	Page   int `json:"page"`
	Limit  int `json:"limit"`
}

type MeetingLogResponse struct {
	Success    bool             `json:"success"`
	TotalCount int              `json:"total_count"`
	Data       []MeetingLogData `json:"data"`
}

type MeetingLogResponseV2 struct {
	Success    bool               `json:"success"`
	TotalCount int                `json:"total_count"`
	Data       []MeetingLogDataV2 `json:"data"`
}

type MeetingLogData struct {
	MeetId    int       `json:"meet_id"`
	Timestamp time.Time `json:"timestamp"`
	Action    string    `json:"action"`
	Actor     string    `json:"actor"`
}

type MeetingLogDataV2 struct {
	MeetId int    `json:"meet_id"`
	Date   string `json:"date"`
	Time   string `json:"time"`
	Action string `json:"action"`
	Actor  string `json:"actor"`
}

func MapMeetingLogData(data *model.MeetingLogData) *MeetingLogData {
	return &MeetingLogData{
		MeetId:    data.MeetId,
		Timestamp: data.Timestamp,
		Action:    data.Action,
		Actor:     data.Actor,
	}
}

func MapToModel(data *MeetingLogData) *model.MeetingLogData {
	return &model.MeetingLogData{
		MeetId:    data.MeetId,
		Timestamp: data.Timestamp,
		Action:    data.Action,
		Actor:     data.Actor,
	}
}
