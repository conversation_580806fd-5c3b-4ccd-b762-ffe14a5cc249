package dto

import (
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/model"
)

type CommentResponse struct {
	Success bool   `json:"success"`
	Comment string `json:"comment"`
}

type CommentLogResponse struct {
	Success bool             `json:"success"`
	Data    []CommentLogData `json:"data"`
}
type CommentLogData struct {
	AccountId  string    `json:"account_id"`
	MeetId     int       `json:"meet_id"`
	NewComment string    `json:"new_comment"`
	OldComment string    `json:"old_comment"`
	CreatedAt  time.Time `json:"created_at"`
}

func ToCommentLogData(data *model.CommentLog) *CommentLogData {
	return &CommentLogData{
		AccountId:  data.AccountId,
		MeetId:     data.MeetId,
		NewComment: data.NewComment,
		OldComment: data.OldComment,
		CreatedAt:  data.CreatedAt,
	}
}
