package handler

import "github.com/gin-gonic/gin"

type MeetingHandler interface {
	FetchMeetingData(c *gin.Context)
	ExportDataToExcel(c *gin.Context)

	RegisterRoutes(r *gin.RouterGroup)
}

type CommentHandler interface {
	InsertComment(c *gin.Context)
	GetComment(c *gin.Context)

	GetListComment(c *gin.Context)

	RegisterRoutes(r *gin.RouterGroup)
}

type StatusHandler interface {
	InsertStatus(c *gin.Context)
	GetStatus(c *gin.Context)

	GetListStatus(c *gin.Context)

	RegisterRoutes(r *gin.RouterGroup)
}

type FilterHandler interface {
	ListFilterOptions(c *gin.Context)

	RegisterRoutes(r *gin.RouterGroup)
}
