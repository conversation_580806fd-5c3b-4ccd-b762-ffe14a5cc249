package handler

import (
	"net/http"
	"strconv"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/service"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"
	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
	"git.inet.co.th/meeting-dashboard-backend/pkg/response"
	"github.com/gin-gonic/gin"
)

type CommentHandlerImpl struct {
	commentService service.CommentService
	authMiddleware *middleware.AuthMiddleware
}

func NewCommentHandler(commentService service.CommentService, authMiddleware *middleware.AuthMiddleware) CommentHandler {
	return &CommentHandlerImpl{
		commentService: commentService,
		authMiddleware: authMiddleware,
	}
}

func (h *CommentHandlerImpl) InsertComment(c *gin.Context) {
	accountID := c.MustGet("accountID").(string)
	fullName := c.MustGet("fullName").(string)
	var req struct {
		Comment string `json:"comment" binding:"required"`
		MeetID  int    `json:"meet_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid request", err)
		apperrors.RespondError(c, appErr)
		return
	}

	err := h.commentService.InsertComment(c, accountID, fullName, req.Comment, req.MeetID)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response.SendOK(c, "Add comment successfully", nil)
}

func (h *CommentHandlerImpl) GetComment(c *gin.Context) {
	meetIDParam := c.Param("id")
	meetID, err := strconv.Atoi(meetIDParam)
	if err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid meet_id", err)
		apperrors.RespondError(c, appErr)
		return
	}

	res, err := h.commentService.GetComment(c, meetID)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response.SendOK(c, "Get comment successfully", res.Comment)
}

func (h *CommentHandlerImpl) GetListComment(c *gin.Context) {
	meetIDParam := c.Param("id")
	meetID, err := strconv.Atoi(meetIDParam)
	if err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid meet_id", err)
		apperrors.RespondError(c, appErr)
		return
	}

	res, err := h.commentService.GetListComment(c, meetID)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response.SendOK(c, "Get comment successfully", res.Data)
}

func (h *CommentHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	r.Use(h.authMiddleware.RequireAuth())

	//Comment
	r.POST("/comment", h.authMiddleware.RequireRole("Editor", "Admin"), h.InsertComment)
	r.GET("/comment/:id", h.authMiddleware.RequireRole("Editor", "Admin", "Viewer"), h.GetComment)

	r.GET("/comment/list/:id", h.authMiddleware.RequireRole("Editor", "Admin"), h.GetListComment)
}
