package handler

import (
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/service"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"
	"git.inet.co.th/meeting-dashboard-backend/pkg/response"
	"github.com/gin-gonic/gin"
)

type FilterHandlerImpl struct {
	filterService  service.FilterService
	authMiddleware *middleware.AuthMiddleware
}

func NewFilterHandler(filterService service.FilterService, authMiddleware *middleware.AuthMiddleware) FilterHandler {
	return &FilterHandlerImpl{
		filterService:  filterService,
		authMiddleware: authMiddleware,
	}
}

func (h *FilterHandlerImpl) ListFilterOptions(c *gin.Context) {
	blockCustomer, err := h.filterService.ListBlockCustomer(c)
	if err != nil {
		c.Error(err)
		return
	}

	blockTeamsale, err := h.filterService.ListBlockTeamSale(c)
	if err != nil {
		c.Error(err)
		return
	}

	presaleClcName, err := h.filterService.ListPresaleClcName(c)
	if err != nil {
		c.Error(err)
		return
	}

	presaleVisitName, err := h.filterService.ListPresaleVisitName(c)
	if err != nil {
		c.Error(err)
		return
	}

	presalePlcName, err := h.filterService.ListPresalePlcName(c)
	if err != nil {
		c.Error(err)
		return
	}

	customerPlc, err := h.filterService.ListCustomerPlc(c)
	if err != nil {
		c.Error(err)
		return
	}

	customerClc, err := h.filterService.ListCustomerClc(c)
	if err != nil {
		c.Error(err)
		return
	}

	dto := &dto.ListFilterOptionsResponse{
		BlockCustomer:    blockCustomer,
		BlockTeamsale:    blockTeamsale,
		CustomerClc:      customerClc,
		PresaleClcName:   presaleClcName,
		PresaleVisitName: presaleVisitName,
		CustomerPlc:      customerPlc,
		PresalePlcName:   presalePlcName,
	}

	response.SendOK(c, "", dto)
}

func (h *FilterHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	r.Use(h.authMiddleware.RequireAuth())

	//Filter Data
	r.GET("/list-filter-ops", h.ListFilterOptions)
}
