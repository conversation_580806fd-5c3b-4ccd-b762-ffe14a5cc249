package handler

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/service"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"

	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
)

type MeetingHandlerImpl struct {
	meetingService service.MeetingService
	authMiddleware *middleware.AuthMiddleware
}

func NewMeetingHandler(meetingService service.MeetingService, authMiddleware *middleware.AuthMiddleware) MeetingHandler {
	return &MeetingHandlerImpl{
		meetingService: meetingService,
		authMiddleware: authMiddleware,
	}
}

func (h *MeetingHandlerImpl) FetchMeetingData(c *gin.Context) {
	var req dto.MeetingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid request", err)
		apperrors.RespondError(c, appErr)
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	res, err := h.meetingService.FetchMeetingData(c, req)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response := dto.SuccessResponse{
		Status:     "success",
		Message:    "Fetch meeting data successfully",
		Data:       res.Data,
		TotalCount: res.TotalCount,
		Page:       req.Page,
		Limit:      req.Limit,
	}

	c.JSON(http.StatusOK, response)
}

func (h *MeetingHandlerImpl) ExportDataToExcel(c *gin.Context) {
	var req dto.MeetingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid request", err)
		apperrors.RespondError(c, appErr)
		return
	}

	filename := "meeting.xlsx"

	excelBytes, err := h.meetingService.ExportDataToExcel(c, req, filename)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelBytes)
}

func (h *MeetingHandlerImpl) GetMeetingData(c *gin.Context) {
	meetIdStr := c.Param("meet_id")
	if meetIdStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "meet_id is required"})
		return
	}

	meetId, err := strconv.Atoi(meetIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "meet_id must be an integer"})
		return
	}

	resp, err := h.meetingService.GetMeetingService(c.Request.Context(), meetId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *MeetingHandlerImpl) UpsertMeetingData(c *gin.Context) {
	var req dto.MeetingServiceRequest
	fullName := c.MustGet("fullName").(string)

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.MeetingServiceResponse{
			Success:        false,
			MeetingService: nil,
		})
		return
	}

	err := h.meetingService.UpsertMeetingService(c.Request.Context(), fullName, req)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			c.JSON(http.StatusNotFound, dto.MeetingServiceResponse{
				Success:        false,
				MeetingService: nil,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, dto.MeetingServiceResponse{
			Success:        false,
			MeetingService: nil,
		})
		return
	}

	c.JSON(http.StatusOK, dto.MeetingServiceResponse{
		Success:        true,
		MeetingService: req.MeetingService,
	})
}

func (h *MeetingHandlerImpl) GetMeetingLogData(c *gin.Context) {
	var req dto.MeetingLogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid request", err)
		apperrors.RespondError(c, appErr)
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	res, err := h.meetingService.GetMeetingLog(c, req)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response := dto.SuccessResponse{
		Status:     "success",
		Message:    "Fetch meeting log data successfully",
		Data:       res.Data,
		TotalCount: res.TotalCount,
		Page:       req.Page,
		Limit:      req.Limit,
	}

	c.JSON(http.StatusOK, response)
}

func (h *MeetingHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	r.Use(h.authMiddleware.RequireAuth())

	//Meeting Data
	r.POST("", h.FetchMeetingData)
	r.POST("/excel", h.ExportDataToExcel)
	r.POST("/service", h.UpsertMeetingData)

	r.POST("/log", h.authMiddleware.RequireRole("Admin"), h.GetMeetingLogData)

	r.GET("/service/:meet_id", h.GetMeetingData)

}
