package handler

import (
	"net/http"
	"strconv"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v1/service"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"
	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
	"git.inet.co.th/meeting-dashboard-backend/pkg/response"
	"github.com/gin-gonic/gin"
)

type StatusHandlerImpl struct {
	statusService  service.StatusService
	authMiddleware *middleware.AuthMiddleware
}

func NewStatusHandlerr(statusService service.StatusService, authMiddleware *middleware.AuthMiddleware) StatusHandler {
	return &StatusHandlerImpl{
		statusService:  statusService,
		authMiddleware: authMiddleware,
	}
}

func (h *StatusHandlerImpl) InsertStatus(c *gin.Context) {
	accountID := c.MustGet("accountID").(string)
	fullName := c.MustGet("fullName").(string)
	var req struct {
		Status string `json:"status" binding:"required"`
		MeetID int    `json:"meet_id" binding:"required"`
	}
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid request", err)
		apperrors.RespondError(c, appErr)
		return
	}

	err := h.statusService.InsertStatus(c, accountID, fullName, req.Status, req.MeetID)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response.SendOK(c, "Add Status successfully", nil)
}

func (h *StatusHandlerImpl) GetStatus(c *gin.Context) {
	meetIDParam := c.Param("id")
	meetID, err := strconv.Atoi(meetIDParam)
	if err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid meet_id", err)
		apperrors.RespondError(c, appErr)
		return
	}

	res, err := h.statusService.GetStatus(c, meetID)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response.SendOK(c, "Get status successfully", res.Status)
}

func (h *StatusHandlerImpl) GetListStatus(c *gin.Context) {
	meetIDParam := c.Param("id")
	meetID, err := strconv.Atoi(meetIDParam)
	if err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid meet_id", err)
		apperrors.RespondError(c, appErr)
		return
	}

	res, err := h.statusService.GetListStatus(c, meetID)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response.SendOK(c, "Get status successfully", res.Data)
}

func (h *StatusHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	r.Use(h.authMiddleware.RequireAuth())

	//Status
	r.POST("/status", h.authMiddleware.RequireRole("Editor", "Admin"), h.InsertStatus)
	r.GET("/status/:id", h.authMiddleware.RequireRole("Editor", "Admin"), h.GetStatus)

	r.GET("/status/list/:id", h.authMiddleware.RequireRole("Editor", "Admin"), h.GetListStatus)
}
