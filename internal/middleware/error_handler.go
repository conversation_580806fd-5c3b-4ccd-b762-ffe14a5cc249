package middleware

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/pkg/response"
)

func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		if len(c.Errors) > 0 {
			err := c.Errors.Last().Err

			statusCode := http.StatusInternalServerError
			typeErr := "internal server error"
			userMessage := "An unexpected error occurred. Please try again later."

			var (
				authErr       *apperrors.AuthError
				validationErr *apperrors.ValidationError
				badRequestErr *apperrors.BadRequestError
				notFoundErr   *apperrors.NotFoundError
				internalErr   *apperrors.InternalServerError
			)

			switch {
			case errors.As(err, &authErr):
				statusCode = http.StatusUnauthorized
				typeErr = "authentication error"
				userMessage = authErr.Error()

			case errors.As(err, &validationErr):
				statusCode = http.StatusBadRequest
				typeErr = "validation error"
				userMessage = validationErr.Error()

			case errors.As(err, &badRequestErr):
				statusCode = http.StatusBadRequest
				typeErr = "bad request error"
				userMessage = badRequestErr.Error()

			case errors.As(err, &notFoundErr):
				statusCode = http.StatusNotFound
				typeErr = "not found error"
				userMessage = notFoundErr.Error()

			case errors.As(err, &internalErr):
				statusCode = http.StatusInternalServerError
				userMessage = internalErr.Error()
			}

			response.SendError(c, statusCode, typeErr, userMessage)
		}
	}
}
