package middleware

import (
	"time"

	"git.inet.co.th/meeting-dashboard-backend/pkg/logger"
	"github.com/gin-gonic/gin"
)

func RequestLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		c.Next()

		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		var errMsg string
		if len(c.Errors) > 0 {
			errMsg = c.Errors.Last().Error()
		}

		if raw != "" {
			path = path + "?" + raw
		}

		logEvent := logger.Log.Info()
		if statusCode >= 400 {
			logEvent = logger.Log.Warn()
		}
		if statusCode >= 500 {
			logEvent = logger.Log.Error()
		}

		logEvent.Str("method", method).
			Str("path", path).
			Int("status", statusCode).
			Dur("latency", latency).
			Str("ip", clientIP).
			Str("user_agent", c.Request.UserAgent()).
			Str("error", errMsg).
			Msg("Request processed")
	}
}
