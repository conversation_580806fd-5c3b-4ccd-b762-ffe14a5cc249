package middleware

import (
	"slices"
	"strings"

	"github.com/gin-gonic/gin"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/jwt"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/store"

	userstore "git.inet.co.th/meeting-dashboard-backend/internal/users/store"
)

type AuthMiddleware struct {
	jwtService   *jwt.Service
	sessionStore store.SessionStore
	userStore    userstore.UserStore
}

func NewAuthMiddleware(jwtService *jwt.Service, sessionStore store.SessionStore, userStore userstore.UserStore) *AuthMiddleware {
	return &AuthMiddleware{
		jwtService:   jwtService,
		sessionStore: sessionStore,
		userStore:    userStore,
	}
}

func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		accessToken, err := extractTokenFromHeader(c)
		if err != nil {
			c.Error(err)
			c.Abort()
			return
		}

		session, err := m.sessionStore.FindByToken(c.Request.Context(), accessToken)
		if err != nil {
			c.Error(apperrors.NewAuthError("Your session has expired. Please sign in again."))
			c.Abort()
			return
		}

		claims, err := m.jwtService.VerifyToken(accessToken, "access")
		if err != nil {
			c.Error(apperrors.NewAuthError("Invalid token type. Please sign in again."))
			c.Abort()
			return
		}

		if claims.Version != m.jwtService.GetCurrentVersion() {
			c.Error(apperrors.NewAuthError("Your authentication token is outdated. Please sign in again."))
			c.Abort()
			return
		}

		c.Set("sessionID", session.SessionID)
		c.Set("fullName", claims.FullName)
		c.Set("accountID", claims.AccountID)
		c.Set("role", claims.Role)

		c.Next()
	}
}

func (m *AuthMiddleware) RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole := c.MustGet("role").(string)

		if slices.Contains(roles, userRole) {
			c.Next()
			return
		}

		c.Error(apperrors.NewAuthError("user does not have permission"))
		c.Abort()
	}
}

func (m *AuthMiddleware) ValidateSession() gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID := c.MustGet("sessionID").(string)
		token, err := extractTokenFromHeader(c)
		if err != nil {
			c.Error(err)
			c.Abort()
			return
		}

		session, err := m.sessionStore.FindByToken(c.Request.Context(), token)
		if err != nil {
			c.Error(apperrors.NewAuthError("Your session has expired. Please sign in again."))
			c.Abort()
			return
		}

		if session.SessionID != sessionID {
			c.Error(apperrors.NewAuthError("session identifier mismatch"))
			c.Abort()
			return
		}

		c.Next()
	}
}

func extractTokenFromHeader(c *gin.Context) (string, error) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return "", apperrors.NewAuthError("authorization header is required")
	}

	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		return "", apperrors.NewAuthError("invalid authorization header format")
	}

	return parts[1], nil
}
