package handler

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/service"
	"git.inet.co.th/meeting-dashboard-backend/pkg/response"
)

type UserHandlerImpl struct {
	userService    service.UserServicer
	authMiddleware *middleware.AuthMiddleware
}

func NewUserHandler(userService service.UserServicer, authMiddleware *middleware.AuthMiddleware) UserHandler {
	return &UserHandlerImpl{
		userService:    userService,
		authMiddleware: authMiddleware,
	}
}

func (h *UserHandlerImpl) GetProfile(c *gin.Context) {
	accountID := c.MustGet("accountID").(string)

	user, err := h.userService.GetProfile(c, accountID)
	if err != nil {
		c.Error(err)
		return
	}

	responsePayload := dto.ToUserProfileResponse(user)
	response.SendOK(c, "User profile retrieved successfully", responsePayload)
}

func (h *UserHandlerImpl) ListUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.DefaultQuery("search", "")

	listResponse, err := h.userService.ListUsers(c.Request.Context(), page, limit, search)
	if err != nil {
		c.Error(err)
		return
	}

	response.SendOK(c, "Users listed successfully", listResponse)
}

func (h *UserHandlerImpl) UpdateUser(c *gin.Context) {
	userID := c.Param("id")

	var req dto.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(apperrors.NewBadRequestError("invalid request"))
		return
	}

	user, err := h.userService.UpdateUser(c.Request.Context(), userID, req)
	if err != nil {
		c.Error(err)
		return
	}

	responsePayload := dto.UpdateUserResponse{
		User: *dto.ToUserProfileResponse(user),
	}

	response.SendOK(c, "User updated successfully", responsePayload)
}

func (h *UserHandlerImpl) DeleteUser(c *gin.Context) {
	userID := c.Param("id")

	err := h.userService.DeleteUser(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	response.SendOK(c, "User successfully deleted", nil)
}

func (h *UserHandlerImpl) InviteUsers(c *gin.Context) {
	var req dto.InviteUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(apperrors.NewBadRequestError("invalid request"))
		return
	}

	inviteResponse, err := h.userService.InviteUsers(c.Request.Context(), req.Role, req.Emails)
	if err != nil {
		c.Error(fmt.Errorf("failed to process invitations: %w", err))
		return
	}

	response.SendOK(c, "Users invited successfully", inviteResponse.Data)
}

// InviteViaXlsxConcurrent handles concurrent invitation via XLSX upload.
// func (h *UserHandlerImpl) InviteViaXlsxConcurrent(c *gin.Context) {
// 	filHeader, err := c.FormFile("file")
// 	if err != nil {
// 		c.Error(apperrors.NewValidationError(fmt.Sprintf("failed to get file: %v", err)))
// 		return
// 	}

// 	file, err := filHeader.Open()
// 	if err != nil {
// 		c.Error(fmt.Errorf("failed to open file: %w", err))
// 		return
// 	}
// 	defer file.Close()

// 	// Use the parser function
// 	invitesByRole, err := parseInviteXLSXBySheetName(file)
// 	if err != nil {
// 		c.Error(err) // Propagate error from parser
// 		return
// 	}

// 	if len(invitesByRole) == 0 {
// 		response.SendOK(c, "No valid roles/emails found in XLSX file to process.", nil)
// 		return
// 	}

// 	// Use WaitGroup to manage concurrent calls for each role
// 	var wg sync.WaitGroup
// 	// Mutex to safely write to the results map from multiple goroutines
// 	var mu sync.Mutex
// 	resultsByRole := make(map[string]*dto.InviteUserResponse)
// 	// Channel to collect errors from goroutines
// 	errorsChan := make(chan error, len(invitesByRole))

// 	for role, emails := range invitesByRole {
// 		if len(emails) == 0 {
// 			fmt.Printf("Info: Skipping role '%s' as it has no emails.\n", role)
// 			continue
// 		}

// 		wg.Add(1)
// 		go func(currentRole string, currentEmails []string) {
// 			defer wg.Done()

// 			fmt.Printf("Info: Concurrently inviting %d users for role '%s'\n", len(currentEmails), currentRole)
// 			// Call the CONCURRENT service method
// 			inviteResponse, err := h.userService.InviteUsersConcurrent(c.Request.Context(), currentRole, currentEmails)

// 			mu.Lock()
// 			if err != nil {
// 				// If the service method itself returned a critical error (e.g., transaction failed)
// 				errorMsg := fmt.Sprintf("critical error processing role %s: %v", currentRole, err)
// 				fmt.Println("Error:", errorMsg)
// 				errorsChan <- fmt.Errorf("%s", errorMsg) // Send error to channel
// 				// Store partial results or error indication if available in inviteResponse
// 				if inviteResponse != nil {
// 					resultsByRole[currentRole] = inviteResponse
// 				} else {
// 					// Create a placeholder if response is nil
// 					resultsByRole[currentRole] = &dto.InviteUserResponse{
// 						Failed: []dto.FailedInvitation{{Email: "N/A", Reason: errorMsg}},
// 					}
// 				}
// 			} else {
// 				// Store successful results
// 				resultsByRole[currentRole] = inviteResponse
// 			}
// 			mu.Unlock()
// 		}(role, emails) // Pass role and emails as arguments
// 	}

// 	// Wait for all role processing goroutines to complete
// 	wg.Wait()
// 	close(errorsChan) // Close channel after all writers are done

// 	// Check if any critical errors occurred
// 	var firstError error
// 	for err := range errorsChan {
// 		if firstError == nil {
// 			firstError = err
// 		}
// 		// Log all errors
// 		fmt.Println("Collected Error:", err)
// 	}

// 	if firstError != nil {
// 		// Send back aggregated results along with an indication of the error
// 		// Using c.JSON directly to include data with an error status
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"message": "Concurrent XLSX invitation process completed with errors",
// 			"error":   firstError.Error(),
// 			"results": resultsByRole,
// 		})
// 		return
// 	}

// 	// If no critical errors, send OK with the aggregated results
// 	response.SendOK(c, "Concurrent XLSX invitation process completed successfully", resultsByRole)
// }

func (h *UserHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	r.Use(h.authMiddleware.RequireAuth())
	r.Use(h.authMiddleware.ValidateSession())

	r.GET("/me", h.GetProfile)

	r.GET("", h.authMiddleware.RequireRole("Editor", "Admin"), h.ListUsers)
	r.PATCH("/:id", h.authMiddleware.RequireRole("Admin"), h.UpdateUser)
	r.DELETE("/:id", h.authMiddleware.RequireRole("Admin"), h.DeleteUser)
	r.POST("/invitations", h.authMiddleware.RequireRole("Admin"), h.InviteUsers)
	// r.POST("/invitations/xlsx/concurrent", h.authMiddleware.RequireRole("Admin"), h.InviteViaXlsxConcurrent)
}
