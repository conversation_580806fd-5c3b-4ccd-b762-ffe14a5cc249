package handler

import (
	"fmt"
	"io"
	"strings"

	"github.com/xuri/excelize/v2"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors" // Assuming apperrors package exists
)

// Defines the column index (0-based) where emails are expected
const emailColumnIndex = 3 // Column D

// Defines the maximum length for an email address
const maxEmailLength = 255

// parseInviteXLSXBySheetName reads an XLSX file from the reader, extracts emails based on
// sheet names (case-insensitive matching for "Admin", "Editor", "Viewer"),
// validates emails, and returns a map where keys are roles and values are slices of valid emails.
func parseInviteXLSXBySheetName(file io.Reader) (map[string][]string, error) {
	xlsxFile, err := excelize.OpenReader(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read xlsx file: %w", err)
	}

	sheetList := xlsxFile.GetSheetList()
	if len(sheetList) == 0 {
		return nil, apperrors.NewValidationError("no sheets found in the workbook")
	}

	invitesByRole := make(map[string][]string)
	processedRoles := make(map[string]bool) // Keep track of roles already processed

	for _, sheetName := range sheetList {
		var role string
		normalizedSheetName := strings.ToLower(strings.TrimSpace(sheetName))

		// Determine role based on sheet name (case-insensitive)
		switch normalizedSheetName {
		case "admin":
			role = "Admin"
		case "edit":
			role = "Editor"
		case "view":
			role = "Viewer"
		default:
			fmt.Printf("Info: Skipping sheet with unrecognized name: %s", sheetName)
			continue // Skip sheets that don't match expected role names
		}

		// Skip if this role was already processed from another sheet (e.g., "Admin" and "admin")
		if processedRoles[role] {
			fmt.Printf("Info: Skipping sheet '%s' as role '%s' was already processed from another sheet.", sheetName, role)
			continue
		}

		rows, err := xlsxFile.GetRows(sheetName)
		if err != nil {
			// Log the error but continue processing other sheets if possible
			fmt.Printf("Error: Failed to get rows from sheet '%s': %v. Skipping this sheet.", sheetName, err)
			continue // Skip this sheet on error
		}

		var emails []string
		// Start from j=1 to skip header row
		for j, row := range rows {
			if j == 0 {
				continue // Skip header row
			}

			if len(row) > emailColumnIndex {
				cell := strings.TrimSpace(row[emailColumnIndex]) // Trim whitespace

				// Basic validation
				if cell == "" {
					// Log or decide policy: skip empty cells silently for now
					continue
				}
				if len(cell) > maxEmailLength {
					// Return an error immediately for invalid data format
					return nil, apperrors.NewValidationError(fmt.Sprintf("email '%s...' in sheet '%s', row %d exceeds maximum length of %d characters", cell[:10], sheetName, j+1, maxEmailLength))
				}
				// Consider adding more robust email format validation here if needed
				// e.g., using a regex or a dedicated library

				emails = append(emails, cell)
			}
			// Silently ignore rows that don't have the email column
		}

		if len(emails) > 0 {
			invitesByRole[role] = emails
			processedRoles[role] = true // Mark role as processed
		} else {
			fmt.Printf("Info: No valid emails found or extracted from sheet '%s' for role '%s'", sheetName, role)
			// Add the role with an empty slice if you want to represent that the sheet was processed but empty
			// invitesByRole[role] = []string{}
			// processedRoles[role] = true
		}
	}

	if len(invitesByRole) == 0 {
		// Return validation error if no data could be extracted from any relevant sheet
		return nil, apperrors.NewValidationError("no valid email data found in sheets named 'Admin', 'Editor', or 'Viewer'")
	}

	return invitesByRole, nil
}
