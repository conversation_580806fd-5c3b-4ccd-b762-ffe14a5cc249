package oneth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/config"
	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/dto"
)

const (
	searchPath = "/api/search_account_by_email"
)

type Provider struct {
	baseURL    string
	clientID   string
	secretKey  string
	httpClient *http.Client
}

func NewProvider(cfg config.Config) *Provider {
	return &Provider{
		baseURL:    cfg.OneThURL,
		clientID:   "566",
		secretKey:  "U7Zhx5k1smIDFx6iS5yaf2EuKOuZ4rF0m2otMBxq",
		httpClient: &http.Client{Timeout: 15 * time.Second},
	}
}

func (p *Provider) SearchByEmail(ctx context.Context, email string) (*dto.OneThSearchResponse, error) {
	baseURL, err := url.Parse(p.baseURL + searchPath)
	if err != nil {
		return nil, apperrors.NewInternalServerError(fmt.Sprintf("failed to parse One.th search URL: %s", err))
	}

	params := url.Values{}
	params.Add("email", email)
	params.Add("client_id", p.clientID)
	params.Add("secret_key", p.secretKey)
	baseURL.RawQuery = params.Encode()

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, baseURL.String(), nil)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to create One.th search request")
	}

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to execute One.th search request")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, apperrors.NewInternalServerError("One.th search request failed")
	}

	var response dto.OneThSearchResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, apperrors.NewInternalServerError("failed to decode One.th search response")
	}

	return &response, nil
}
