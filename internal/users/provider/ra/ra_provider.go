package ra

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/config"
	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/dto"
)

const (
	userDetailPath = "/api/vMonk/externalApi/detailUserByAccountId"
)

type Provider struct {
	baseURL     string
	bearerToken string
	httpClient  *http.Client
}

func NewProvider(cfg config.Config) *Provider {
	return &Provider{
		baseURL:     cfg.RAOneThURL,
		bearerToken: cfg.RAOneThBearerToken,
		httpClient:  &http.Client{Timeout: 15 * time.Second},
	}
}

func (p *Provider) GetUserDetailsByAccountID(ctx context.Context, accountID string) (*dto.RAOneThDetailResponse, error) {
	requestURL := p.baseURL + userDetailPath
	requestBody := map[string]string{
		"accountId": accountID,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to marshal request body")
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, apperrors.NewInternalServerError(fmt.Sprintf("failed to create request for URL: %s with accountID: %s", requestURL, accountID))
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+p.bearerToken)

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to execute RA request")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, apperrors.NewInternalServerError("failed to get user details from RA API")
	}

	var response dto.RAOneThDetailResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, apperrors.NewInternalServerError("failed to decode RA response")
	}

	return &response, nil
}
