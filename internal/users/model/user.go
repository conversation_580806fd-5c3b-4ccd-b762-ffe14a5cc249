package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type User struct {
	ID         primitive.ObjectID `bson:"_id,omitempty"`
	AccountID  string             `bson:"accountId"`
	Name       string             `bson:"name"`
	Email      string             `bson:"email"`
	Role       string             `bson:"role"`
	EmployeeID string             `bson:"employeeId"`
	CreatedAt  time.Time          `bson:"createdAt"`
	UpdatedAt  time.Time          `bson:"updatedAt"`
}
