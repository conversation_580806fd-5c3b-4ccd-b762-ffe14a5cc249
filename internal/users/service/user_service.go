package service

import (
	"context"
	"errors"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/jwt"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/model"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/provider"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/store"

	sessionstore "git.inet.co.th/meeting-dashboard-backend/internal/auth/store"
)

const (
	DefaultPage  = 1
	DefaultLimit = 10
	MaxLimit     = 100
)

type UserService struct {
	jwtService    *jwt.Service
	userStore     store.UserStore
	sessionStore  sessionstore.SessionStore
	oneThProvider provider.OneThProvider
	raProvider    provider.RAProvider
}

func NewUserService(
	jwtService *jwt.Service,
	userStore store.UserStore,
	sessionStore sessionstore.SessionStore,
	oneThProvider provider.OneThProvider,
	raProvider provider.RAProvider,
) UserServicer {
	return &UserService{
		jwtService:    jwtService,
		userStore:     userStore,
		sessionStore:  sessionStore,
		oneThProvider: oneThProvider,
		raProvider:    raProvider,
	}
}

func (s *UserService) GetUserByID(ctx context.Context, accountID string) (*model.User, error) {
	user, err := s.userStore.FindByAccountID(ctx, accountID)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return nil, apperrors.NewNotFoundError("user", accountID)
		}
		return nil, apperrors.NewInternalServerError("error finding user")
	}

	return user, nil
}

func (s *UserService) GetProfile(ctx context.Context, accountID string) (*model.User, error) {
	user, err := s.userStore.FindByAccountID(ctx, accountID)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return nil, apperrors.NewNotFoundError("user", accountID)
		}
		return nil, apperrors.NewInternalServerError("error finding user")
	}

	return user, nil
}

func (s *UserService) ListUsers(ctx context.Context, page, limit int, search string) (*dto.UserListResponse, error) {
	if page <= 0 {
		page = DefaultPage
	}
	if limit <= 0 {
		limit = DefaultLimit
	}
	if limit > MaxLimit {
		limit = MaxLimit
	}

	filter := bson.M{}
	if search != "" {
		filter["name"] = bson.M{"$regex": search, "$options": "i"} // case-insensitive search
	}

	findOptions := options.Find()
	findOptions.SetSkip(int64((page - 1) * limit))
	findOptions.SetLimit(int64(limit))

	users, err := s.userStore.FindAll(ctx, filter, findOptions)
	if err != nil {
		return nil, apperrors.NewInternalServerError("error finding users")
	}

	totalCount, err := s.userStore.Count(ctx, filter)
	if err != nil {
		return nil, apperrors.NewInternalServerError("error counting users")
	}

	userDTOs := make([]dto.UserProfileResponse, len(users))
	for i, user := range users {
		userDTOs[i] = *dto.ToUserProfileResponse(user)
	}

	return &dto.UserListResponse{
		Users:      userDTOs,
		TotalCount: totalCount,
		Page:       page,
		Limit:      limit,
	}, nil
}

func (s *UserService) UpdateUser(ctx context.Context, userID string, input dto.UpdateUserRequest) (*model.User, error) {
	user, err := s.userStore.FindByAccountID(ctx, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return nil, apperrors.NewNotFoundError("user", userID)
		}
		return nil, apperrors.NewInternalServerError("error finding user")
	}

	roleChanged := input.Role != "" && input.Role != user.Role

	if input.Role != "" {
		user.Role = input.Role
	}

	if err := s.userStore.Update(ctx, user); err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return nil, apperrors.NewNotFoundError("user", userID)
		}
		return nil, apperrors.NewInternalServerError("error updating user")
	}

	if roleChanged {
		if err := s.sessionStore.DeleteAllForUser(ctx, user.AccountID); err != nil {
			return user, apperrors.NewInternalServerError("failed to invalidate user sessions")
		}
	}

	return user, nil
}

func (s *UserService) DeleteUser(ctx context.Context, userID string) error {
	s.sessionStore.DeleteAllForUser(ctx, userID)

	err := s.userStore.Delete(ctx, userID)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return apperrors.NewNotFoundError("user", userID)
		}
		return apperrors.NewInternalServerError("error deleting user")
	}

	return nil
}

func (s *UserService) InviteUsers(ctx context.Context, role string, emails []string) (*dto.InviteUserResponse, error) {
	response := &dto.InviteUserResponse{
		Data: []dto.InviteUserData{},
	}

	for _, email := range emails {
		existingUser, err := s.userStore.FindByEmail(ctx, email)

		if err == nil && existingUser != nil {
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    fmt.Sprintf("User already exists: %v", apperrors.ErrDuplicateEntry),
				Success:   false,
			})
			continue
		}

		if err != nil && !errors.Is(err, apperrors.ErrNotFound) {
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    fmt.Sprintf("Error checking existing user: %v", err),
				Success:   false,
			})
			continue
		}

		oneThData, err := s.oneThProvider.SearchByEmail(ctx, email)
		if err != nil {
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    fmt.Sprintf("Failed to find user in One.th: %v", err),
				Success:   false,
			})
			continue
		}

		if oneThData == nil || oneThData.Data.ID == "" {
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    fmt.Sprintf("No valid account data found in One.th: %v", apperrors.ErrNotFound),
				Success:   false,
			})
			continue
		}
		accountID := oneThData.Data.ID

		existingUserByID, err := s.userStore.FindByAccountID(ctx, accountID)
		if err == nil && existingUserByID != nil {
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    fmt.Sprintf("User with account ID %s already exists: %v", accountID, apperrors.ErrDuplicateEntry),
				Success:   false,
			})
			continue
		}
		if err != nil && !errors.Is(err, apperrors.ErrNotFound) {
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    fmt.Sprintf("Error checking existing user by account ID %s: %v", accountID, err),
				Success:   false,
			})
			continue
		}

		raData, err := s.raProvider.GetUserDetailsByAccountID(ctx, accountID)
		if err != nil {
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    fmt.Sprintf("Failed to get user details from RA for account %s: %v", accountID, err),
				Success:   false,
			})
			continue
		}

		if raData == nil || raData.Result == nil {
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    fmt.Sprintf("No result data found in RA for account %s: %v", accountID, apperrors.ErrNotFound),
				Success:   false,
			})
			continue
		}

		name := fmt.Sprintf("%s %s", oneThData.Data.FirstNameTh, oneThData.Data.LastNameTh)
		employeeID := ""

		if len(raData.Result) > 0 && len(raData.Result[0].UserInCompany) > 0 {
			employeeID = raData.Result[0].UserInCompany[0].EmployeeID
		}

		newUser := &model.User{
			AccountID:  accountID,
			Email:      email,
			Name:       name,
			Role:       role,
			EmployeeID: employeeID,
		}

		if err := s.userStore.Create(ctx, newUser); err != nil {
			reason := fmt.Errorf("failed to create user in database: %w", err).Error()
			if errors.Is(err, apperrors.ErrDuplicateEntry) {
				reason = fmt.Sprintf("User creation conflict (likely duplicate accountId %s): %v", accountID, apperrors.ErrDuplicateEntry)
			}
			response.Data = append(response.Data, dto.InviteUserData{
				AccountID: "",
				Email:     email,
				Reason:    reason,
				Success:   false,
			})
			continue
		}

		response.Data = append(response.Data, dto.InviteUserData{
			AccountID: accountID,
			Email:     email,
			Reason:    "",
			Success:   true,
		})
	}

	return response, nil
}

// Define a struct to hold results from each goroutine for concurrent processing
// type processResult struct {
// 	User         *model.User
// 	Failure      *dto.FailedInvitation
// 	ProcessError error // For critical errors during processing, not validation/DB checks
// }

// // InviteUsersConcurrent processes user invitations concurrently and uses bulk insert within a transaction.
// func (s *UserService) InviteUsersConcurrent(ctx context.Context, role string, emails []string) (*dto.InviteUserResponse, error) {
// 	response := &dto.InviteUserResponse{
// 		Successful: []dto.InvitedUserData{},
// 		Failed:     []dto.FailedInvitation{},
// 	}

// 	if len(emails) == 0 {
// 		return response, nil // Nothing to process
// 	}

// 	var wg sync.WaitGroup
// 	resultsChan := make(chan processResult, len(emails))

// 	// --- Phase 1: Concurrent Data Fetching and Validation ---
// 	for _, email := range emails {
// 		wg.Add(1)

// 		go func(currentEmail string) {
// 			defer wg.Done()
// 			goroutineCtx := ctx // Use the original context for simplicity

// 			// 1. Check if user already exists by email
// 			existingUser, err := s.userStore.FindByEmail(goroutineCtx, currentEmail)
// 			if err == nil && existingUser != nil {
// 				resultsChan <- processResult{Failure: &dto.FailedInvitation{Email: currentEmail, Reason: fmt.Sprintf("User already exists (email match): %v", apperrors.ErrDuplicateEntry)}}
// 				return
// 			}
// 			if err != nil && !errors.Is(err, apperrors.ErrNotFound) {
// 				resultsChan <- processResult{Failure: &dto.FailedInvitation{Email: currentEmail, Reason: fmt.Sprintf("Error checking existing user by email: %v", err)}}
// 				return
// 			}

// 			// 2. Search in One.th
// 			oneThData, err := s.oneThProvider.SearchByEmail(currentEmail)
// 			if err != nil {
// 				resultsChan <- processResult{Failure: &dto.FailedInvitation{Email: currentEmail, Reason: fmt.Sprintf("Failed to find user in One.th: %v", err)}}
// 				return
// 			}
// 			if oneThData == nil || oneThData.Data.ID == "" {
// 				resultsChan <- processResult{Failure: &dto.FailedInvitation{Email: currentEmail, Reason: "User not found in One.th or missing ID"}}
// 				return
// 			}
// 			accountID := oneThData.Data.ID

// 			// 3. Check if user already exists by AccountID
// 			existingUserByID, err := s.userStore.FindByAccountID(goroutineCtx, accountID)
// 			if err == nil && existingUserByID != nil {
// 				resultsChan <- processResult{Failure: &dto.FailedInvitation{Email: currentEmail, Reason: fmt.Sprintf("User already exists (account ID match): %v", apperrors.ErrDuplicateEntry)}}
// 				return
// 			}
// 			if err != nil && !errors.Is(err, apperrors.ErrNotFound) {
// 				resultsChan <- processResult{Failure: &dto.FailedInvitation{Email: currentEmail, Reason: fmt.Sprintf("Error checking existing user by account ID: %v", err)}}
// 				return
// 			}

// 			// 4. Get details from RA
// 			raData, err := s.raProvider.GetUserDetailsByAccountID(accountID)
// 			if err != nil {
// 				resultsChan <- processResult{Failure: &dto.FailedInvitation{Email: currentEmail, Reason: fmt.Sprintf("Failed to get RA details: %v", err)}}
// 				return
// 			}
// 			if raData == nil || raData.Result == nil {
// 				resultsChan <- processResult{Failure: &dto.FailedInvitation{Email: currentEmail, Reason: "RA details not found or result is nil"}}
// 				return
// 			}

// 			// 5. Prepare user data
// 			name := fmt.Sprintf("%s %s", oneThData.Data.FirstNameTh, oneThData.Data.LastNameTh)
// 			employeeID := ""
// 			if len(raData.Result) > 0 && len(raData.Result[0].UserInCompany) > 0 {
// 				employeeID = raData.Result[0].UserInCompany[0].EmployeeID
// 			}

// 			newUser := &model.User{
// 				AccountID:  accountID,
// 				Email:      currentEmail,
// 				Name:       name,
// 				Role:       role,
// 				EmployeeID: employeeID,
// 				// CreatedAt/UpdatedAt will be set by store
// 			}

// 			resultsChan <- processResult{User: newUser}

// 		}(email)
// 	}

// 	// Goroutine to close channel once all workers are done
// 	go func() {
// 		wg.Wait()
// 		close(resultsChan)
// 	}()

// 	// --- Collect Results ---
// 	usersToCreate := []*model.User{}
// 	var firstProcessError error

// 	for result := range resultsChan {
// 		if result.Failure != nil {
// 			response.Failed = append(response.Failed, *result.Failure)
// 		} else if result.User != nil {
// 			usersToCreate = append(usersToCreate, result.User)
// 		} else if result.ProcessError != nil && firstProcessError == nil {
// 			firstProcessError = result.ProcessError
// 		}
// 	}

// 	// If a critical error happened during fetching/validation, return early
// 	if firstProcessError != nil {
// 		// Mark all potentially valid users as failed because processing stopped
// 		for _, user := range usersToCreate {
// 			response.Failed = append(response.Failed, dto.FailedInvitation{
// 				Email:  user.Email,
// 				Reason: fmt.Sprintf("Processing aborted due to critical error: %v", firstProcessError),
// 			})
// 		}
// 		return response, fmt.Errorf("critical error during concurrent processing: %w", firstProcessError)
// 	}

// 	// --- Phase 2: Database Insertion (Using Bulk Insert in Store) ---
// 	if len(usersToCreate) > 0 {
// 		fmt.Printf("Info: Attempting bulk insert for %d users with role '%s'\n", len(usersToCreate), role)
// 		// Call the new store method which handles the transaction and bulk insert
// 		err := s.userStore.CreateMultipleUsers(ctx, usersToCreate)
// 		if err != nil {
// 			// Bulk insert failed (e.g., transaction error, duplicate key within the batch)
// 			errorMsg := fmt.Sprintf("Bulk insert failed: %v", err)
// 			fmt.Println("Error:", errorMsg)
// 			// Mark ALL users intended for this batch as failed
// 			for _, user := range usersToCreate {
// 				response.Failed = append(response.Failed, dto.FailedInvitation{
// 					Email:  user.Email,
// 					Reason: errorMsg, // Use the error from CreateMultipleUsers
// 				})
// 			}
// 			// Return the error from the store method
// 			return response, err // Propagate the specific error (e.g., duplicate entry)
// 		}

// 		// Bulk insert successful, add all users to the successful list
// 		fmt.Printf("Info: Bulk insert successful for %d users with role '%s'\n", len(usersToCreate), role)
// 		for _, user := range usersToCreate {
// 			response.Successful = append(response.Successful, dto.InvitedUserData{
// 				AccountID:  user.AccountID,
// 				Email:      user.Email,
// 				Name:       user.Name,
// 				Role:       user.Role,
// 				EmployeeID: user.EmployeeID,
// 			})
// 		}
// 	}

// 	return response, nil // Overall success (even if some individual emails failed validation earlier)
// }
