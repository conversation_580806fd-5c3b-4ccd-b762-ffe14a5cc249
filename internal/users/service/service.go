package service

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/users/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/model"
)

type UserServicer interface {
	GetUserByID(ctx context.Context, accountID string) (*model.User, error)
	GetProfile(ctx context.Context, accountID string) (*model.User, error)

	ListUsers(ctx context.Context, page, limit int, search string) (*dto.UserListResponse, error)
	UpdateUser(ctx context.Context, userID string, input dto.UpdateUserRequest) (*model.User, error)
	DeleteUser(ctx context.Context, userID string) error
	InviteUsers(ctx context.Context, role string, emails []string) (*dto.InviteUserResponse, error)
	// InviteUsersConcurrent(ctx context.Context, role string, emails []string) (*dto.InviteUserResponse, error)
}
