package store

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/users/model"
)

const UsersCollectionName = "users"

type UserStoreImpl struct {
	collection *mongo.Collection
}

func NewUserStore(db *mongo.Database) UserStore {
	return &UserStoreImpl{
		collection: db.Collection(UsersCollectionName),
	}
}

func (r *UserStoreImpl) FindByAccountID(ctx context.Context, accountID string) (*model.User, error) {
	var user model.User
	filter := bson.M{"accountId": accountID}

	err := r.collection.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, apperrors.ErrNotFound
		}
		return nil, apperrors.ErrInternalServerError
	}

	return &user, nil
}

func (r *UserStoreImpl) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	filter := bson.M{"email": email}

	err := r.collection.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, apperrors.ErrNotFound
		}
		return nil, apperrors.ErrInternalServerError
	}

	return &user, nil
}

func (r *UserStoreImpl) Create(ctx context.Context, user *model.User) error {
	if user.CreatedAt.IsZero() {
		user.CreatedAt = time.Now()
	}
	if user.UpdatedAt.IsZero() {
		user.UpdatedAt = time.Now()
	}

	_, err := r.collection.InsertOne(ctx, user)
	if err != nil {
		var writeException mongo.WriteException
		if errors.As(err, &writeException) {
			for _, writeError := range writeException.WriteErrors {
				if writeError.Code == 11000 {
					return apperrors.ErrDuplicateEntry
				}
			}
		}
		return apperrors.ErrInternalServerError
	}

	return nil
}

func (r *UserStoreImpl) FindAll(ctx context.Context, filter interface{}, opts *options.FindOptions) ([]*model.User, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var users []*model.User
	if err := cursor.All(ctx, &users); err != nil {
		return nil, err
	}

	return users, nil
}

func (r *UserStoreImpl) Update(ctx context.Context, user *model.User) error {
	user.UpdatedAt = time.Now()

	filter := bson.M{"accountId": user.AccountID}
	update := bson.M{"$set": user}
	opts := options.Update().SetUpsert(false)

	res, err := r.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return apperrors.ErrInternalServerError
	}

	if res.MatchedCount == 0 {
		return apperrors.ErrNotFound
	}

	return nil
}

func (r *UserStoreImpl) Delete(ctx context.Context, accountID string) error {
	filter := bson.M{"accountId": accountID}

	res, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return apperrors.ErrInternalServerError
	}

	if res.DeletedCount == 0 {
		return apperrors.ErrNotFound
	}

	return nil
}

func (r *UserStoreImpl) Count(ctx context.Context, filter interface{}) (int64, error) {
	return r.collection.CountDocuments(ctx, filter)
}

// CreateMultipleUsers inserts multiple users within a single transaction using InsertMany.
func (r *UserStoreImpl) CreateMultipleUsers(ctx context.Context, users []*model.User) error {
	if len(users) == 0 {
		return nil // Nothing to insert
	}

	// Prepare documents for insertion
	documents := make([]interface{}, len(users))
	now := time.Now()
	for i, user := range users {
		if user.CreatedAt.IsZero() {
			user.CreatedAt = now
		}
		if user.UpdatedAt.IsZero() {
			user.UpdatedAt = now
		}
		documents[i] = user
	}

	// Use the database client from the collection to start a session
	client := r.collection.Database().Client()
	session, err := client.StartSession()
	if err != nil {
		return fmt.Errorf("failed to start database session for bulk insert: %w", err)
	}
	defer session.EndSession(ctx)

	// Define the transaction callback
	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		// Use InsertMany for bulk insertion
		_, insertErr := r.collection.InsertMany(sessCtx, documents)
		if insertErr != nil {
			// Check for duplicate key error (common in bulk inserts)
			var writeException mongo.WriteException
			if errors.As(insertErr, &writeException) {
				for _, writeError := range writeException.WriteErrors {
					if writeError.Code == 11000 { // Duplicate key error code
						// Return a specific application error for duplicate entry
						return nil, fmt.Errorf("bulk user creation failed due to duplicate entry: %w", apperrors.ErrDuplicateEntry)
					}
				}
			}
			// Return the original error if it's not a duplicate key or other specific error
			return nil, fmt.Errorf("error during bulk user insert: %w", insertErr)
		}
		return nil, nil // Success
	}

	// Execute the transaction
	_, txErr := session.WithTransaction(ctx, callback)
	if txErr != nil {
		// The error from the callback (including duplicate key error) is returned here
		return fmt.Errorf("database transaction failed for bulk insert: %w", txErr)
	}

	return nil // Transaction successful
}
