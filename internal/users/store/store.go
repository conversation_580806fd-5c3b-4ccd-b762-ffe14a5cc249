package store

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/users/model"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type UserStore interface {
	FindByAccountID(ctx context.Context, accountID string) (*model.User, error)
	FindByEmail(ctx context.Context, email string) (*model.User, error)
	Create(ctx context.Context, user *model.User) error

	// Admin operations
	FindAll(ctx context.Context, filter interface{}, opts *options.FindOptions) ([]*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, accountID string) error
	Count(ctx context.Context, filter interface{}) (int64, error)
	// CreateMultipleUsers inserts multiple users within a single transaction.
	// It returns an error if the transaction fails or if any user insertion fails (e.g., duplicate key).
	CreateMultipleUsers(ctx context.Context, users []*model.User) error
}
