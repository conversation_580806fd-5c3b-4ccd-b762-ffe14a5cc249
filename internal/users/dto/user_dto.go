package dto

import (
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/users/model"
)

type UserProfileResponse struct {
	AccountID  string    `json:"accountId"`
	Name       string    `json:"name"`
	Email      string    `json:"email"`
	Role       string    `json:"role"`
	EmployeeID string    `json:"employeeId"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

type UserListRequest struct {
	Page  int `form:"page" json:"page"`
	Limit int `form:"limit" json:"limit"`
}

type UserListResponse struct {
	Users      []UserProfileResponse `json:"users"`
	TotalCount int64                 `json:"totalCount"`
	Page       int                   `json:"page"`
	Limit      int                   `json:"limit"`
}

type UpdateUserRequest struct {
	Role string `json:"role"`
}

type UpdateUserResponse struct {
	User UserProfileResponse `json:"user"`
}

type DeleteUserResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

func ToUserProfileResponse(user *model.User) *UserProfileResponse {
	return &UserProfileResponse{
		AccountID:  user.AccountID,
		Name:       user.Name,
		Email:      user.Email,
		Role:       user.Role,
		EmployeeID: user.EmployeeID,
		CreatedAt:  user.CreatedAt,
		UpdatedAt:  user.UpdatedAt,
	}
}
