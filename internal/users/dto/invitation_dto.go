package dto

type InviteUserRequest struct {
	Role   string   `json:"role" binding:"required"`
	Emails []string `json:"emails" binding:"required,min=1,dive,email"`
}

type InviteUserResponse struct {
	Data []InviteUserData `json:"data"`
}
type InviteUserData struct {
	AccountID string `json:"accountId"`
	Email     string `json:"email"`
	Reason    string `json:"reason"`
	Success   bool   `json:"success"`
}

// {
// 	"accountId:"****************",
// 	"email": "<EMAIL>",
// 	"reason": "",
// 	"success": true
// }

// type InviteUserResponse struct {
// 	Successful []InvitedUserData  `json:"successful"`
// 	Failed     []FailedInvitation `json:"failed"`
// }

// type InvitedUserData struct {
// 	AccountID  string `json:"accountId"`
// 	Email      string `json:"email"`
// 	Name       string `json:"name"`
// 	Role       string `json:"role"`
// 	EmployeeID string `json:"employeeId,omitempty"`
// 	Success    bool   `json:"success"`
// }

// type FailedInvitation struct {
// 	Email   string `json:"email"`
// 	Reason  string `json:"reason"`
// 	Success string `json:"success"`
// }

type OneThSearchResponse struct {
	Result       string        `json:"result"`
	Data         OneThUserData `json:"data"`
	ErrorMessage interface{}   `json:"errorMessage"`
	Code         int           `json:"code"`
}

type OneThUserData struct {
	ID          string        `json:"id"`
	FirstNameTh string        `json:"first_name_th"`
	LastNameTh  string        `json:"last_name_th"`
	Email       []OneThEmail  `json:"email"`
	User        OneThUserInfo `json:"user"`
}

type OneThEmail struct {
	Email string `json:"email"`
	Pivot struct {
		PrimaryFlag string `json:"primary_flg"`
	} `json:"pivot"`
}

type OneThUserInfo struct {
	Username string `json:"username"`
}

type RAOneThDetailResponse struct {
	Success bool              `json:"success"`
	Result  []RAOneThUserData `json:"result"`
}

type RAOneThUserData struct {
	FirstNameTh   string               `json:"firstNameTh"`
	LastNameTh    string               `json:"lastNameTh"`
	FirstNameEn   string               `json:"firstNameEn"`
	LastNameEn    string               `json:"lastNameEn"`
	AccountID     string               `json:"accountId"`
	Email         string               `json:"email"`
	Tel           string               `json:"tel"`
	UserInCompany []RAOneThCompanyData `json:"userInCompany"`
}

type RAOneThCompanyData struct {
	EmployeeID string `json:"employeeId"`
}
