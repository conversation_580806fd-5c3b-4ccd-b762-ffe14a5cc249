package utils

import "go.mongodb.org/mongo-driver/bson"

func ExprNe(field string, value any) bson.M {
	return bson.M{"$expr": bson.M{"$ne": bson.A{field, value}}}
}

func ExprEq(field string, value any) bson.M {
	return bson.M{"$expr": bson.M{"$eq": bson.A{field, value}}}
}

func ExprIn(field string, value any) bson.M {
	return bson.M{"$expr": bson.M{"$in": bson.A{field, value}}}
}

func ExprAnd(conditions ...bson.M) bson.M {
	return bson.M{"$expr": bson.M{"$and": conditions}}
}

func ExprOr(conditions ...bson.M) bson.M {
	return bson.M{"$expr": bson.M{"$or": conditions}}
}

func ExprNot(condition bson.M) bson.M {
	return bson.M{"$expr": bson.M{"$not": condition}}
}

func ExprIfNull(field string, value any) bson.M {
	return bson.M{"$ifNull": bson.A{field, value}}
}

func ExprConcat(fields ...string) bson.M {
	return bson.M{"$concat": fields}
}

func ExprDateFromString(fieldName string) bson.M {
	return bson.M{"$dateFromString": bson.M{"dateString": "$" + fieldName}}
}

func ExprDateToString(fieldName string) bson.M {
	return bson.M{"$dateToString": bson.M{"format": "%d/%m/%Y", "date": fieldName}}
}

func ExprSwitch(branches []bson.M, defaultVal any) bson.M {
	return bson.M{"$switch": bson.M{"branches": branches, "default": defaultVal}}
}

func ExprCase(conditions ...bson.M) bson.M {
	return bson.M{"$case": conditions}
}
