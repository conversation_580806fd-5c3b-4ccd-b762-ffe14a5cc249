package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/migration/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/migration/provider"
	"git.inet.co.th/meeting-dashboard-backend/internal/migration/store"
)

type MigrateServiceImpl struct {
	cvmProvider  provider.CvmProvider
	migrateStore store.MigrateStore
}

func NewMigrateService(cvmProvider provider.CvmProvider, migrateStore store.MigrateStore) MigrateService {
	return &MigrateServiceImpl{
		cvmProvider:  cvmProvider,
		migrateStore: migrateStore,
	}
}

func (s *MigrateServiceImpl) MigrateMeeting(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingResponse, error) {
	layout := "2006-01-02"
	date := time.Now().Format(layout)
	customNoti := fmt.Sprintf("ข้อมูล Platform-meeting วันที่ %s", date)

	meetData, err := s.cvmProvider.MigrateMeetingInfo(ctx, body)
	if err != nil {
		return nil, err
	}

	numWorkers := 10
	jobs := make(chan struct {
		index int
		doc   map[string]interface{}
	}, len(meetData.Data))

	var wg sync.WaitGroup

	for w := 0; w < numWorkers; w++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for job := range jobs {
				select {
				case <-ctx.Done():
					fmt.Printf("Context canceled, skipping doc %d\n", job.index)
					return
				default:
					data, err := s.ConvertMessage(ctx, job.doc)
					if err != nil {
						fmt.Printf("Error converting doc %d: %v\n", job.index, err)
						messageFailed := fmt.Sprintf("ข้อมูลของ Platform Meeting ของวันที่ %s\n ข้อผิดพลาด : %v", date, err)
						err := SendNotiOnePlatform(messageFailed, customNoti)
						if err != nil {
							fmt.Printf("Error sending notification: %v\n", err)
						}
						continue
					}
					upsertErr := s.migrateStore.UpsertInfoData(ctx, []interface{}{data})
					if upsertErr != nil {
						fmt.Printf("Error upserting doc %d: %v\n", job.index, upsertErr)
						messageFailed := fmt.Sprintf("ข้อมูลของ Platform Meeting ของวันที่ %s\n ข้อผิดพลาด : %v", date, err)
						err := SendNotiOnePlatform(messageFailed, customNoti)
						if err != nil {
							fmt.Printf("Error sending notification: %v\n", err)
						}
						continue
					}
				}
			}
		}()
	}

	for i, doc := range meetData.Data {
		jobs <- struct {
			index int
			doc   map[string]interface{}
		}{index: i, doc: doc}
	}
	close(jobs)

	wg.Wait()

	// messageSuccess := fmt.Sprintf("ข้อมูลของ Platform Meeting ของวันที่ %s\n จำนวนข้อมูล %d", date, len(meetData.Data))

	count, today, err := s.migrateStore.CountByTodayMeetCreateAt(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count today's documents: %w", err)
	}

	countDataDB, err := s.migrateStore.CountMeetingData(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count Database documents: %w", err)
	}

	messageSuccess := fmt.Sprintf(
		"ข้อมูลของ Platform Meeting ของวันที่ %s\nจำนวนข้อมูลจากต้นทาง : %d\nจำนวนข้อมูลที่เพิ่มเข้าใน DB (วันที่เพิ่มเข้ามา: %s) : %d \nจำนวนข้อมูลทั้งหมดใน DB : %d",
		date,
		len(meetData.Data),
		today,
		count,
		countDataDB,
	)

	err = SendNotiOnePlatform(messageSuccess, customNoti)
	if err != nil {
		return nil, err
	}

	return &dto.MeetingResponse{
		Message: meetData.Message,
		Status:  meetData.Status,
	}, nil
}

func (s *MigrateServiceImpl) MigrateMeetingPeriod(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingResponse, error) {
	// layout := "2006-01-02"
	// date := time.Now().Format(layout)
	// customNoti := fmt.Sprintf("ข้อมูล Platform-meeting วันที่ %s", date)

	meetData, err := s.cvmProvider.MigrateMeetingPeriod(ctx, body)
	if err != nil {
		return nil, err
	}

	numWorkers := 10
	jobs := make(chan struct {
		index int
		doc   map[string]interface{}
	}, len(meetData.Data))

	var wg sync.WaitGroup

	for w := 0; w < numWorkers; w++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for job := range jobs {
				select {
				case <-ctx.Done():
					fmt.Printf("Context canceled, skipping doc %d\n", job.index)
					return
				default:
					upsertErr := s.migrateStore.UpsertPeriodData(ctx, []interface{}{job.doc})
					if upsertErr != nil {
						fmt.Printf("Error upserting doc %d: %v\n", job.index, upsertErr)
						// messageFailed := fmt.Sprintf("ข้อมูลของ Platform Meeting ของวันที่ %s\n ข้อผิดพลาด : %v", date, err)
						// err := SendNotiOnePlatform(messageFailed, customNoti)
						// if err != nil {
						// 	fmt.Printf("Error sending notification: %v\n", err)
						// }
						continue
					}
				}
			}
		}()
	}

	for i, doc := range meetData.Data {
		jobs <- struct {
			index int
			doc   map[string]interface{}
		}{index: i, doc: doc}
	}
	close(jobs)

	wg.Wait()

	// messageSuccess := fmt.Sprintf("ข้อมูลของ Platform Meeting ของวันที่ %s\n จำนวนข้อมูล %d", date, len(meetData.Data))

	// err = SendNotiOnePlatform(messageSuccess, customNoti)
	// if err != nil {
	// 	return nil, err
	// }

	return &dto.MeetingResponse{
		Message: meetData.Message,
		Status:  meetData.Status,
	}, nil
}

func (s *MigrateServiceImpl) PreJoinMeeting(ctx context.Context) (*dto.MeetingResponse, error) {
	err := s.migrateStore.PreJoinMeeting(ctx)
	if err != nil {
		return nil, err
	}

	return &dto.MeetingResponse{
		Message: "Success",
		Status:  200,
	}, nil
}

func (s *MigrateServiceImpl) PreJoinMeetingStatus(ctx context.Context) (*dto.MeetingResponse, error) {
	err := s.migrateStore.PreJoinMeetingStatus(ctx)
	if err != nil {
		return nil, err
	}

	return &dto.MeetingResponse{
		Message: "Success",
		Status:  200,
	}, nil
}

func (s *MigrateServiceImpl) PreJoinMeetingPeriod(ctx context.Context) (*dto.MeetingResponse, error) {

	err := s.migrateStore.PreJoinMeetingPeriod(ctx)
	if err != nil {
		return nil, err
	}

	return &dto.MeetingResponse{
		Message: "Success",
		Status:  200,
	}, nil
}

func (s *MigrateServiceImpl) ConvertMessage(ctx context.Context, data map[string]interface{}) (map[string]interface{}, error) {
	var nums []int
	if data["cus_clc"] != "null" && data["cus_clc"] != nil && data["cus_clc"] != "" && data["cus_clc"] != "[]" {
		cusClcStr, ok := data["cus_clc"].(string)
		if !ok {
			fmt.Println("Error: cus_clc is not a string")
		}
		err := json.Unmarshal([]byte(cusClcStr), &nums)
		if err != nil {
			return nil, fmt.Errorf("error unmarshalling cus_clc: %w", err)
		}
		var clcStr []string
		for _, num := range nums {
			switch num {
			case 0:
				clcStr = append(clcStr, "-")
			case 1:
				clcStr = append(clcStr, "BCP")
			case 2:
				clcStr = append(clcStr, "Shapeup")
			case 3:
				clcStr = append(clcStr, "VA Scan")
			case 4:
				clcStr = append(clcStr, "Update Benefit (MNSP Contact , Sale Contact , ช่องทาง Noc)")
			case 5:
				clcStr = append(clcStr, "AD")
			}
		}
		data["cus_clc"] = clcStr
	} else {
		var clcStr []string
		clcStr = append(clcStr, "-")
		data["cus_clc"] = clcStr
	}

	if data["plc"] != "null" && data["plc"] != nil && data["plc"] != "" && data["plc"] != "[]" {
		cusPlcStr, ok := data["plc"].(string)
		if !ok {
			fmt.Println("Error: cus_plc is not a string")
		}
		err := json.Unmarshal([]byte(cusPlcStr), &nums)
		if err != nil {
			return nil, fmt.Errorf("error unmarshalling cus_plc: %w", err)
		}
		var plcStr []string
		for _, num := range nums {
			switch num {
			case 0:
				plcStr = append(plcStr, "-")
			case 1:
				plcStr = append(plcStr, "Meter Partner")
			case 2:
				plcStr = append(plcStr, "One Platform")
			case 3:
				plcStr = append(plcStr, "Model Refferal/Reseller")
			case 4:
				plcStr = append(plcStr, "Shapeup")
			case 5:
				plcStr = append(plcStr, "BCP Test")
			case 6:
				plcStr = append(plcStr, "VA SCAN")
			case 7:
				plcStr = append(plcStr, "Port Monitor")
			case 8:
				plcStr = append(plcStr, "Backup short Alert")
			case 9:
				plcStr = append(plcStr, "Cross Platform")
			case 10:
				plcStr = append(plcStr, "Cross Service")
			}
		}
		data["cus_plc"] = plcStr
	} else {
		var clcStr []string
		clcStr = append(clcStr, "-")
		data["cus_plc"] = clcStr
	}

	if data["cus_visit"] != "null" && data["cus_visit"] != nil && data["cus_visit"] != "" && data["cus_visit"] != "[]" {
		cusVisitStr, ok := data["cus_visit"].(string)
		if !ok {
			fmt.Println("Error: cus_visit is not a string")
		}
		err := json.Unmarshal([]byte(cusVisitStr), &nums)
		if err != nil {
			return nil, fmt.Errorf("error unmarshalling cus_visit: %w", err)
		}
		var visitStr []string
		for _, num := range nums {
			switch num {
			case 0:
				visitStr = append(visitStr, "-")
			case 1:
				visitStr = append(visitStr, "Update Service")
			case 2:
				visitStr = append(visitStr, "เหตุผลอื่นๆ")
			}
		}
		data["cus_visit"] = visitStr
	} else {
		var clcStr []string
		clcStr = append(clcStr, "-")
		data["cus_visit"] = clcStr
	}

	return data, nil
}

func SendNotiOnePlatform(message string, customNoti string) error {
	requestURL := "https://one-platform.one.th/chat/api/v1/chatbot-api/message"

	body := &dto.NotiRequest{
		To:         "61747eb9-af11-4658-bc6a-be598e879fc4",
		Message:    message,
		Type:       "text",
		CustomNoti: customNoti,
	}

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("error marshalling request body: %w", err)
	}

	req, err := http.NewRequestWithContext(context.Background(), http.MethodPost, requestURL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Add("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJib3RfaWQiOiJmMjY4M2FlMC05MDhiLTRmZDUtYjQ1OC01ZGQ5MjQyZDY0NDYiLCJib3Rfc2lnbmF0dXJlIjoiMmExMTc3YTIzNGY3ZWM4N2IxMWViOTZjOWIyOTM0NjA3MmM5YTI2ODVlNWY3MWIxYzk4MDg4MTQyYWRmMzA3MiIsImVudiI6InByZCIsInVzZXJfaWQiOiI3NzJlOWNiNS03YjVkLTQ3MDEtYTYyMi00MDE4NGU5ZTA4YWIifQ.AL4C75npnNV0myM3hSD59AiCll6ifM-md2ZmtBIA_wk")
	req.Header.Add("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("received non-OK HTTP status: %s", resp.Status)
	}

	return nil
}
