package service

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/migration/dto"
)

type MigrateService interface {
	//ดึงข้อมูลจาก API มาเก็บลง DB
	MigrateMeeting(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingResponse, error)
	MigrateMeetingPeriod(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingResponse, error)

	//Join ข้อมูลจาก meeting กับ metadata เป็น meeting-join
	PreJoinMeeting(ctx context.Context) (*dto.MeetingResponse, error)
	PreJoinMeetingStatus(ctx context.Context) (*dto.MeetingResponse, error)

	//Join ข้อมูลจาก meeting กับ meeting-period เป็น meeting-join
	PreJoinMeetingPeriod(ctx context.Context) (*dto.MeetingResponse, error)
}
