package dto

type MeetingRequest struct {
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

type MeetingResponse struct {
	Message string `json:"message"`
	Status  int    `json:"status"`
}

type MeetingData struct {
	Message string                   `json:"message"`
	Status  int                      `json:"status"`
	Data    []map[string]interface{} `json:"data"`
}

type NotiRequest struct {
	To         string `json:"to"`
	Message    string `json:"message"`
	Type       string `json:"type"`
	CustomNoti string `json:"custom_notification"`
}
