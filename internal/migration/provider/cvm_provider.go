package provider

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/config"
	"git.inet.co.th/meeting-dashboard-backend/internal/migration/dto"
)

const (
	MeetingInfoPath   = "/api/v6/public/on_period_meet_info"
	MeetingPeriodPath = "/api/v6/public/mom_info_by_meet/on_period"
)

type Provider struct {
	baseURL    string
	clientID   string
	secretKey  string
	httpClient *http.Client
}

func NewProvider(cfg config.Config) CvmProvider {
	return &Provider{
		baseURL:    cfg.MeetVisitURL,
		clientID:   cfg.MeetVisitClientID,
		secretKey:  cfg.MeetVisitSecretKey,
		httpClient: &http.Client{Timeout: 15 * time.Second},
	}
}

func (p *Provider) MigrateMeetingInfo(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingData, error) {
	requestURL := p.baseURL + MeetingInfoPath
	requestBody := map[string]string{
		"start_date": body.StartDate,
		"start_time": body.StartTime,
		"end_date":   body.EndDate,
		"end_time":   body.EndTime,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, bytes.NewBuffer(jsonBody))

	if err != nil {
		fmt.Println(err)
	}
	req.Header.Add("Client-ID", p.clientID)
	req.Header.Add("Client-Secret", p.secretKey)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
	}
	defer res.Body.Close()

	var response *dto.MeetingData
	err = json.NewDecoder(res.Body).Decode(&response)
	if err != nil {
		return nil, err
	}

	return response, nil

}

func (p *Provider) MigrateMeetingPeriod(ctx context.Context, body dto.MeetingRequest) (*dto.MeetingData, error) {
	requestURL := p.baseURL + MeetingPeriodPath
	requestBody := map[string]string{
		"start_date": body.StartDate,
		"start_time": body.StartTime,
		"end_date":   body.EndDate,
		"end_time":   body.EndTime,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, bytes.NewBuffer(jsonBody))

	if err != nil {
		fmt.Println(err)
	}
	req.Header.Add("Client-ID", p.clientID)
	req.Header.Add("Client-Secret", p.secretKey)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
	}
	defer res.Body.Close()

	var response *dto.MeetingData
	err = json.NewDecoder(res.Body).Decode(&response)
	if err != nil {
		return nil, err
	}

	return response, nil

}
