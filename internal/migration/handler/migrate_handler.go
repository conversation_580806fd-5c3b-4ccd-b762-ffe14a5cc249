package handler

import (
	"net/http"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/migration/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/migration/service"
	"github.com/gin-gonic/gin"
)

type MigrateHandlerImpl struct {
	migrateService service.MigrateService
}

func NewMigrateHandler(migrateService service.MigrateService) MigrateHandler {
	return &MigrateHandlerImpl{
		migrateService: migrateService,
	}
}

func (h *MigrateHandlerImpl) MigrateMeeting(c *gin.Context) {
	var req dto.MeetingRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if req.StartDate == "" && req.StartTime == "" && req.EndDate == "" && req.EndTime == "" {
		layout := "2006-01-02"
		req.StartDate = "2024-01-01"
		req.StartTime = "00:00:00"
		req.EndDate = time.Now().Format(layout)
		req.EndTime = "23:59:59"
	}

	response, err := h.migrateService.MigrateMeeting(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process meeting migration: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *MigrateHandlerImpl) MigrateMeetingPeriod(c *gin.Context) {
	var req dto.MeetingRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if req.StartDate == "" && req.StartTime == "" && req.EndDate == "" && req.EndTime == "" {
		layout := "2006-01-02"
		req.StartDate = "2024-01-01"
		req.StartTime = "00:00:00"
		req.EndDate = time.Now().Format(layout)
		req.EndTime = "23:59:59"
	}

	response, err := h.migrateService.MigrateMeetingPeriod(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process meeting period migration: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *MigrateHandlerImpl) PreJoinMeeting(c *gin.Context) {
	response, err := h.migrateService.PreJoinMeeting(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process pre join meeting : " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *MigrateHandlerImpl) PreJoinMeetingPeriod(c *gin.Context) {
	response, err := h.migrateService.PreJoinMeetingPeriod(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process pre join meeting : " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *MigrateHandlerImpl) MigrateAndJoinMeeting(c *gin.Context) {
	var req dto.MeetingRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if req.StartDate == "" && req.StartTime == "" && req.EndDate == "" && req.EndTime == "" {
		req.StartDate = "2024-01-01"
		req.StartTime = "00:00:00"
		req.EndDate = time.Now().Format(time.DateOnly)
		req.EndTime = "23:59:59"
	}

	if _, err := h.migrateService.MigrateMeeting(c.Request.Context(), req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process meeting migration: " + err.Error()})
		return
	}

	if _, err := h.migrateService.MigrateMeetingPeriod(c.Request.Context(), req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process meeting-period migration: " + err.Error()})
		return
	}

	if _, err := h.migrateService.PreJoinMeeting(c.Request.Context()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process pre join meeting : " + err.Error()})
		return
	}

	if _, err := h.migrateService.PreJoinMeetingStatus(c.Request.Context()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process pre join meeting-status : " + err.Error()})
		return
	}

	response, err := h.migrateService.PreJoinMeetingPeriod(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process pre join meeting-period : " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *MigrateHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	migrate := r.Group("/")

	migrate.POST("/meeting", h.MigrateMeeting)
	migrate.POST("/meeting-period", h.MigrateMeetingPeriod)
	migrate.POST("/pre-join-meeting", h.PreJoinMeeting)
	migrate.POST("/pre-join-period", h.PreJoinMeetingPeriod)

	migrate.POST("/meeting-and-join", h.MigrateAndJoinMeeting)
}
