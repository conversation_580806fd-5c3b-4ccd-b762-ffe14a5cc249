package store

import (
	"context"
)

type MigrateStore interface {
	UpsertInfoData(ctx context.Context, data []interface{}) error
	UpsertPeriodData(ctx context.Context, data []interface{}) error
	PreJoinMeeting(ctx context.Context) error
	PreJoinMeetingStatus(ctx context.Context) error
	PreJoinMeetingPeriod(ctx context.Context) error
	CountMeetingData(ctx context.Context) (int64, error)
	// UpdateData(ctx context.Context, data dto.MeetingResponse) error
	CountByTodayMeetCreateAt(ctx context.Context) (int64, string, error)
}
