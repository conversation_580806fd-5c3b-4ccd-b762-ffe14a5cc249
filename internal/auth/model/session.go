package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Session struct {
	ID           primitive.ObjectID `bson:"_id,omitempty"`
	SessionID    string             `bson:"sessionId"`
	AccountID    string             `bson:"accountId"`
	Token        string             `bson:"token"`
	RefreshToken string             `bson:"refreshToken"`
	IPAddress    string             `bson:"ipAddress,omitempty"`
	UserAgent    string             `bson:"userAgent,omitempty"`
	TokenVersion string             `bson:"tokenVersion,omitempty"`
	CreatedAt    time.Time          `bson:"createdAt"`
	UpdatedAt    time.Time          `bson:"updatedAt"`
}
