package auth

import (
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
)

// ParsePrivate<PERSON><PERSON> parses an RSA private key from a PEM-encoded string.
func ParsePrivateKey(keyPem string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(keyPem))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the private key")
	}

	privKey, err := x509.ParsePKCS1PrivateKey(block.Bytes) // Common for RSA private keys
	if err != nil {
		// If PKCS1 fails, try PKCS8. PKCS8 is a more general format that can also contain RSA keys.
		key, errPkcs8 := x509.ParsePKCS8PrivateKey(block.Bytes)
		if errPkcs8 != nil {
			// Return a combined error if both parsing attempts fail.
			return nil, fmt.Errorf("failed to parse private key as PKCS1 (err: %v) or P<PERSON>CS8 (err: %v)", err, errPkcs8)
		}
		var ok bool
		privKey, ok = key.(*rsa.PrivateKey)
		if !ok {
			return nil, fmt.Errorf("key parsed from PKCS8 is not an RSA private key")
		}
	}

	return privKey, nil
}

// DecryptRSAOAEP decrypts a base64-encoded ciphertext using RSA-OAEP.
// The hash function used is SHA1.
// The label, if used during encryption, must match. Here it's assumed to be empty or nil.
func DecryptRSAOAEP(base64Ciphertext string, privKey *rsa.PrivateKey) (string, error) {
	// Decode the base64 ciphertext
	ciphertext, err := base64.StdEncoding.DecodeString(base64Ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode ciphertext from base64: %w", err)
	}

	// The label parameter must match the value given when encrypting.
	// If no label was used during encryption, it should be nil or an empty byte slice.
	// Using nil for the label as it's a common default if no specific label was used.
	var label []byte = nil

	// The 'random' parameter in rsa.DecryptOAEP is legacy and ignored for OAEP decryption. Pass nil.
	// SHA256 is used as the hash function for OAEP.
	plaintext, err := rsa.DecryptOAEP(sha1.New(), nil, privKey, ciphertext, label)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt RSA-OAEP: %w", err)
	}

	return string(plaintext), nil
}
