package dto

import (
	"time"
)

type SignInInput struct {
	Username  string
	Password  string
	IPAddress string
	UserAgent string
}

type SignInOutput struct {
	Token            string
	RefreshToken     string
	ExpiresAt        time.Time
	RefreshExpiresAt time.Time
}

type SignInRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type SignInResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
}

type SignOutRequest struct {
	Token        string `json:"token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token,omitempty"`
}

type RefreshTokenInput struct {
	RefreshToken string
	IPAddress    string
	UserAgent    string
}

type OneThResponse struct {
	Result       string `json:"result"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
	AccountID    string `json:"account_id"`
	Email        string `json:"login_by"`
}
