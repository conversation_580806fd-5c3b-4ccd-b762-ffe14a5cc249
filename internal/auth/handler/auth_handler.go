package handler

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/service"
	"git.inet.co.th/meeting-dashboard-backend/pkg/response"

	authCrypto "git.inet.co.th/meeting-dashboard-backend/internal/auth"
)

const (
	RefreshTokenCookieName = "refresh_token"
	CookieMaxAge           = 7 * 24 * 60 * 60
	CookiePath             = "/api/v1/auth/"
)

type AuthHandlerImpl struct {
	authService      service.AuthServicer
	rsaPrivateKeyPEM string
}

func NewAuthHandler(authService service.AuthServicer, rsaPrivateKeyPEM string) AuthHandler {
	return &AuthHandlerImpl{
		authService:      authService,
		rsaPrivateKeyPEM: rsaPrivateKeyPEM,
	}
}

func (h *AuthHandlerImpl) SignIn(c *gin.Context) {
	var req dto.SignInRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(apperrors.NewBadRequestError("invalid request format"))
		return
	}

	privateKey, err := authCrypto.ParsePrivateKey(h.rsaPrivateKeyPEM)
	if err != nil {
		c.Error(apperrors.NewInternalServerError("server configuration error, please try again later"))
		return
	}

	decryptedUsername, err := authCrypto.DecryptRSAOAEP(req.Username, privateKey)
	if err != nil {
		c.Error(apperrors.NewBadRequestError("invalid username or password"))
		return
	}

	decryptedPassword, err := authCrypto.DecryptRSAOAEP(req.Password, privateKey)
	if err != nil {
		c.Error(apperrors.NewBadRequestError("invalid username or password"))
		return
	}

	input := dto.SignInInput{
		Username:  decryptedUsername,
		Password:  decryptedPassword,
		IPAddress: c.ClientIP(),
		UserAgent: c.Request.UserAgent(),
	}

	output, err := h.authService.SignIn(c.Request.Context(), input)
	if err != nil {
		c.Error(err)
		return
	}

	h.setRefreshTokenCookie(c, output.RefreshToken, output.RefreshExpiresAt)

	responsePayload := dto.SignInResponse{
		Token:     output.Token,
		ExpiresAt: output.ExpiresAt,
	}

	response.SendOK(c, "Successfully signed in", responsePayload)
}

// DecryptTest is a handler for testing RSA-OAEP decryption.
func (h *AuthHandlerImpl) DecryptTest(c *gin.Context) {
	var req dto.DecryptTestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(apperrors.NewBadRequestError("invalid request format: " + err.Error()))
		return
	}

	privateKey, err := authCrypto.ParsePrivateKey(h.rsaPrivateKeyPEM)
	if err != nil {
		c.Error(apperrors.NewInternalServerError("server configuration error, cannot perform decryption test"))
		return
	}

	decryptedText, err := authCrypto.DecryptRSAOAEP(req.EncryptedText, privateKey)
	if err != nil {
		c.Error(apperrors.NewBadRequestError("decryption failed: " + err.Error()))
		return
	}

	responsePayload := dto.DecryptTestResponse{
		DecryptedText: decryptedText,
	}

	response.SendOK(c, "Decryption successful", responsePayload)
}

func (h *AuthHandlerImpl) RefreshToken(c *gin.Context) {
	refreshToken, err := h.getRefreshTokenFromCookie(c)
	if err != nil {
		c.Error(err)
		return
	}

	input := dto.RefreshTokenInput{
		RefreshToken: refreshToken,
		IPAddress:    c.ClientIP(),
		UserAgent:    c.Request.UserAgent(),
	}

	output, err := h.authService.RefreshToken(c.Request.Context(), input)
	if err != nil {
		c.Error(err)
		return
	}

	h.setRefreshTokenCookie(c, output.RefreshToken, output.RefreshExpiresAt)

	responsePayload := dto.SignInResponse{
		Token:     output.Token,
		ExpiresAt: output.ExpiresAt,
	}

	response.SendOK(c, "Token refreshed successfully", responsePayload)
}

func (h *AuthHandlerImpl) SignOut(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	refreshToken, _ := h.getRefreshTokenFromCookie(c)

	var token string
	if authHeader != "" {
		parts := strings.Split(authHeader, " ")
		if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
			token = parts[1]
		}
	}

	if token == "" {
		var req dto.SignOutRequest
		if err := c.ShouldBindJSON(&req); err == nil && req.Token != "" {
			token = req.Token
		}
	}

	if token == "" && refreshToken == "" {
		c.Error(apperrors.NewBadRequestError("token or refresh token is required"))
		return
	}

	h.clearRefreshTokenCookie(c)

	var err error
	if token != "" {
		err = h.authService.SignOut(c.Request.Context(), token, refreshToken)
	} else if refreshToken != "" {
		err = h.authService.SignOutByRefreshToken(c.Request.Context(), refreshToken)
	}

	if err != nil {
		c.Error(err)
		return
	}

	response.SendOK(c, "Successfully signed out", nil)
}

func (h *AuthHandlerImpl) setRefreshTokenCookie(c *gin.Context, refreshToken string, expires time.Time) {
	maxAge := CookieMaxAge
	if !expires.IsZero() {
		maxAge = int(time.Until(expires).Seconds())
	}

	isSecure := gin.Mode() == gin.ReleaseMode
	isHTTP := gin.Mode() == gin.ReleaseMode
	domain := ""

	sameSiteMode := http.SameSiteLaxMode
	if isSecure {
		sameSiteMode = http.SameSiteNoneMode
	}

	c.SetSameSite(sameSiteMode)
	c.SetCookie(
		RefreshTokenCookieName,
		refreshToken,
		maxAge,
		CookiePath,
		domain,
		isSecure,
		isHTTP,
	)
}

func (h *AuthHandlerImpl) clearRefreshTokenCookie(c *gin.Context) {
	isSecure := gin.Mode() == gin.ReleaseMode
	isHTTP := gin.Mode() == gin.ReleaseMode
	domain := ""

	sameSiteMode := http.SameSiteLaxMode
	if isSecure {
		sameSiteMode = http.SameSiteNoneMode
	}

	c.SetSameSite(sameSiteMode)
	c.SetCookie(
		RefreshTokenCookieName,
		"",
		-1,
		CookiePath,
		domain,
		isSecure,
		isHTTP,
	)
}

func (h *AuthHandlerImpl) getRefreshTokenFromCookie(c *gin.Context) (string, error) {
	refreshToken, err := c.Cookie(RefreshTokenCookieName)

	if err != nil || refreshToken == "" {
		var req dto.RefreshTokenRequest
		if err := c.ShouldBindJSON(&req); err == nil && req.RefreshToken != "" {
			return req.RefreshToken, nil
		}
		return "", apperrors.NewBadRequestError("refresh token is required")
	}

	return refreshToken, nil
}

func (h *AuthHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	r.POST("/signin", h.SignIn)
	r.POST("/signout", h.SignOut)
	r.POST("/refresh", h.RefreshToken)
	r.POST("/decrypt-test", h.DecryptTest)
}
