package oneth

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/dto"
)

const (
	path = "/api/oauth/getpwd"
)

type Config struct {
	URL      string
	ClientID string
	Secret   string
}

type Service struct {
	config     Config
	httpClient *http.Client
}

func NewService(config Config) *Service {
	return &Service{
		config:     config,
		httpClient: &http.Client{Timeout: 10 * time.Second},
	}
}

func (s *Service) PasswordGrant(ctx context.Context, username, password string) (*dto.OneThResponse, error) {
	requestURL := s.config.URL + path
	requestBody := map[string]string{
		"grant_type":    "password",
		"client_id":     s.config.ClientID,
		"client_secret": s.config.Secret,
		"username":      username,
		"password":      password,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to marshal request body")
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, apperrors.NewInternalServerError(fmt.Sprintf("failed to create request for URL: %s with username: %s", requestURL, username))
	}

	req.Header.Add("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to execute One.th request")
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return nil, apperrors.NewAuthError("invalid credentials.")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, apperrors.NewAuthError("failed to authenticate with One.th")
	}

	var response dto.OneThResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to decode One.th response")
	}

	return &response, nil
}
