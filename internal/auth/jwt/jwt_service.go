package jwt

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"

	"git.inet.co.th/meeting-dashboard-backend/internal/users/model"
)

type Config struct {
	SecretKey     string
	Expiry        time.Duration
	Version       string
	RefreshExpiry time.Duration
}

type CustomClaims struct {
	AccountID string `json:"accountId"`
	FullName  string `json:"fullName"`
	Email     string `json:"email"`
	Role      string `json:"role"`
	Version   string `json:"ver,omitempty"`
	TokenType string `json:"type,omitempty"`
	jwt.RegisteredClaims
}

type TokenPair struct {
	AccessToken      string
	RefreshToken     string
	ExpiresAt        time.Time
	RefreshExpiresAt time.Time
	TokenVersion     string
}

type Service struct {
	config Config
}

func NewService(config Config) *Service {
	if config.Version == "" {
		config.Version = "1"
	}

	if config.RefreshExpiry == 0 {
		config.RefreshExpiry = 7 * 24 * time.Hour
	}

	return &Service{
		config: config,
	}
}

func (s *Service) GenerateTokenPair(user *model.User) (*TokenPair, error) {
	accessToken, accessExpiry, err := s.generateAccessToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, refreshExpiry, err := s.generateRefreshToken(user.AccountID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:      accessToken,
		RefreshToken:     refreshToken,
		ExpiresAt:        accessExpiry,
		RefreshExpiresAt: refreshExpiry,
		TokenVersion:     s.config.Version,
	}, nil
}

func (s *Service) RefreshTokenPair(refreshToken string, user *model.User) (*TokenPair, error) {
	claims, err := s.VerifyToken(refreshToken, "refresh")
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	if claims.AccountID != user.AccountID {
		return nil, fmt.Errorf("refresh token does not match user")
	}

	accessToken, accessExpiry, err := s.generateAccessToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new access token: %w", err)
	}

	newRefreshToken, refreshExpiry, err := s.generateRefreshToken(user.AccountID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:      accessToken,
		RefreshToken:     newRefreshToken,
		ExpiresAt:        accessExpiry,
		RefreshExpiresAt: refreshExpiry,
		TokenVersion:     s.config.Version,
	}, nil
}

func (s *Service) generateAccessToken(user *model.User) (string, time.Time, error) {
	expirationTime := time.Now().Add(s.config.Expiry)
	claims := &CustomClaims{
		AccountID: user.AccountID,
		FullName:  user.Name,
		Email:     user.Email,
		Role:      user.Role,
		Version:   s.config.Version,
		TokenType: "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.NewString(),
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   user.AccountID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.config.SecretKey))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expirationTime, nil
}

func (s *Service) generateRefreshToken(accountID string) (string, time.Time, error) {
	expirationTime := time.Now().Add(s.config.RefreshExpiry)
	claims := &CustomClaims{
		AccountID: accountID,
		Version:   s.config.Version,
		TokenType: "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.NewString(),
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   accountID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.config.SecretKey))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expirationTime, nil
}

func (s *Service) VerifyToken(tokenString string, expectedType string) (*CustomClaims, error) {
	claims := &CustomClaims{}

	token, err := jwt.ParseWithClaims(tokenString, claims, func(t *jwt.Token) (interface{}, error) {
		if _, ok := t.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method")
		}
		return []byte(s.config.SecretKey), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("token is invalid")
	}

	if claims.Version != s.config.Version {
		return nil, fmt.Errorf("token version mismatch")
	}

	if expectedType != "" && claims.TokenType != expectedType {
		return nil, fmt.Errorf("invalid token type: expected %s, got %s", expectedType, claims.TokenType)
	}

	return claims, nil
}

func (s *Service) RefreshAccessToken(refreshToken string, user *model.User) (string, time.Time, error) {
	claims, err := s.VerifyToken(refreshToken, "refresh")
	if err != nil {
		return "", time.Time{}, fmt.Errorf("invalid refresh token: %w", err)
	}

	if claims.AccountID != user.AccountID {
		return "", time.Time{}, fmt.Errorf("refresh token does not match user")
	}

	return s.generateAccessToken(user)
}

func (s *Service) GenerateToken(user *model.User) (string, time.Time, error) {
	return s.generateAccessToken(user)
}

func (s *Service) GetSecretKey() string {
	return s.config.SecretKey
}

func (s *Service) GetCurrentVersion() string {
	return s.config.Version
}
