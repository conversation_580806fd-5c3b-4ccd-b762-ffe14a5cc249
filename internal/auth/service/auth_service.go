package service

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/jwt"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/model"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/provider/oneth"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/store"

	usermodel "git.inet.co.th/meeting-dashboard-backend/internal/users/model"
	userstore "git.inet.co.th/meeting-dashboard-backend/internal/users/store"
)

type AuthService struct {
	userRepo     userstore.UserStore
	sessionRepo  store.SessionStore
	jwtService   *jwt.Service
	oneThService *oneth.Service
}

func NewAuthService(
	userRepo userstore.UserStore,
	sessionRepo store.SessionStore,
	jwtService *jwt.Service,
	oneThService *oneth.Service,
) AuthServicer {
	return &AuthService{
		userRepo:     userRepo,
		sessionRepo:  sessionRepo,
		jwtService:   jwtService,
		oneThService: oneThService,
	}
}

func (s *AuthService) SignIn(ctx context.Context, input dto.SignInInput) (*dto.SignInOutput, error) {
	oneThResp, err := s.oneThService.PasswordGrant(ctx, input.Username, input.Password)
	if err != nil {
		return nil, err
	}

	var user *usermodel.User
	user, err = s.userRepo.FindByAccountID(ctx, oneThResp.AccountID)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return nil, apperrors.NewInternalServerError("user data inconsistency")
		}
		return nil, apperrors.NewInternalServerError("error retrieving user data")
	}

	tokenPair, err := s.jwtService.GenerateTokenPair(user)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to create session token")
	}

	refreshExpiresAt := tokenPair.RefreshExpiresAt

	newSession := &model.Session{
		SessionID:    uuid.NewString(),
		AccountID:    user.AccountID,
		Token:        tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		TokenVersion: tokenPair.TokenVersion,
		IPAddress:    input.IPAddress,
		UserAgent:    input.UserAgent,
	}

	err = s.sessionRepo.FindOneAndUpdate(ctx, newSession)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to create session")
	}

	output := &dto.SignInOutput{
		Token:            tokenPair.AccessToken,
		RefreshToken:     tokenPair.RefreshToken,
		ExpiresAt:        tokenPair.ExpiresAt,
		RefreshExpiresAt: refreshExpiresAt,
	}

	return output, nil
}

func (s *AuthService) RefreshToken(ctx context.Context, input dto.RefreshTokenInput) (*dto.SignInOutput, error) {
	session, err := s.sessionRepo.FindByRefreshToken(ctx, input.RefreshToken)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return nil, apperrors.NewAuthError("invalid or expired refresh token")
		}
		return nil, apperrors.NewInternalServerError("error finding session")
	}

	user, err := s.userRepo.FindByAccountID(ctx, session.AccountID)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return nil, apperrors.NewNotFoundError("user", session.AccountID)
		}
		return nil, apperrors.NewInternalServerError("error retrieving user data")
	}

	tokenPair, err := s.jwtService.RefreshTokenPair(input.RefreshToken, user)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to refresh token")
	}

	session.Token = tokenPair.AccessToken
	session.RefreshToken = tokenPair.RefreshToken
	session.TokenVersion = tokenPair.TokenVersion
	session.IPAddress = input.IPAddress
	session.UserAgent = input.UserAgent

	err = s.sessionRepo.FindOneAndUpdate(ctx, session)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to update session")
	}

	output := &dto.SignInOutput{
		Token:            tokenPair.AccessToken,
		RefreshToken:     tokenPair.RefreshToken,
		ExpiresAt:        tokenPair.ExpiresAt,
		RefreshExpiresAt: tokenPair.RefreshExpiresAt,
	}

	return output, nil
}

func (s *AuthService) SignOut(ctx context.Context, token string, refreshToken string) error {
	session, err := s.sessionRepo.FindByToken(ctx, token)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			if refreshToken != "" {
				return s.SignOutByRefreshToken(ctx, refreshToken)
			}
			return apperrors.NewAuthError("invalid or expired session token")
		}
		return apperrors.NewInternalServerError("error finding session")
	}

	err = s.sessionRepo.Delete(ctx, session.SessionID)
	if err != nil {
		if !errors.Is(err, apperrors.ErrNotFound) {
			return fmt.Errorf("failed to invalidate session: %w", err)
		}
	}

	return nil
}

func (s *AuthService) SignOutByRefreshToken(ctx context.Context, refreshToken string) error {
	session, err := s.sessionRepo.FindByRefreshToken(ctx, refreshToken)
	if err != nil {
		if errors.Is(err, apperrors.ErrNotFound) {
			return apperrors.NewAuthError("invalid or expired refresh token")
		}
		return apperrors.NewInternalServerError("error finding session")
	}

	err = s.sessionRepo.Delete(ctx, session.SessionID)
	if err != nil {
		if !errors.Is(err, apperrors.ErrNotFound) {
			return apperrors.NewInternalServerError("failed to invalidate session")
		}
	}

	return nil
}
