package service

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/auth/dto"
)

type AuthServicer interface {
	SignIn(ctx context.Context, input dto.SignInInput) (*dto.SignInOutput, error)
	SignOut(ctx context.Context, token string, refreshToken string) error
	SignOutByRefreshToken(ctx context.Context, refreshToken string) error
	RefreshToken(ctx context.Context, input dto.RefreshTokenInput) (*dto.SignInOutput, error)
}
