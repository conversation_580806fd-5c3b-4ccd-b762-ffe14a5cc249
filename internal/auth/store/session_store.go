package store

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.inet.co.th/meeting-dashboard-backend/internal/apperrors"
	"git.inet.co.th/meeting-dashboard-backend/internal/auth/model"
)

const (
	SessionsCollectionName = "sessions"
)

type SessionStoreImpl struct {
	sessionCollection *mongo.Collection
}

func NewSessionStore(db *mongo.Database) SessionStore {
	return &SessionStoreImpl{
		sessionCollection: db.Collection(SessionsCollectionName),
	}
}

func (r *SessionStoreImpl) FindByToken(ctx context.Context, token string) (*model.Session, error) {
	var session model.Session
	filter := bson.M{"token": token}

	err := r.sessionCollection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, apperrors.ErrNotFound
		}
		return nil, apperrors.ErrInternalServerError
	}

	return &session, nil
}

func (r *SessionStoreImpl) FindByRefreshToken(ctx context.Context, refreshToken string) (*model.Session, error) {
	var session model.Session
	filter := bson.M{"refreshToken": refreshToken}

	err := r.sessionCollection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, apperrors.ErrNotFound
		}
		return nil, apperrors.ErrInternalServerError
	}

	return &session, nil
}

func (r *SessionStoreImpl) FindOneAndUpdate(ctx context.Context, session *model.Session) error {
	if session.CreatedAt.IsZero() {
		session.CreatedAt = time.Now()
	}
	if session.UpdatedAt.IsZero() {
		session.UpdatedAt = time.Now()
	}

	filter := bson.M{"accountId": session.AccountID}
	update := bson.M{"$set": session}
	opts := options.Update().SetUpsert(true)

	_, err := r.sessionCollection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return apperrors.ErrInternalServerError
	}

	return nil
}

func (r *SessionStoreImpl) Delete(ctx context.Context, sessionID string) error {
	filter := bson.M{"sessionId": sessionID}

	result, err := r.sessionCollection.DeleteOne(ctx, filter)
	if err != nil {
		return apperrors.ErrInternalServerError
	}

	if result.DeletedCount == 0 {
		return apperrors.ErrNotFound
	}

	return nil
}

func (r *SessionStoreImpl) FindByAccountID(ctx context.Context, accountID string) (*model.Session, error) {
	var session model.Session
	filter := bson.M{"accountId": accountID}

	err := r.sessionCollection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, apperrors.ErrNotFound
		}
		return nil, apperrors.ErrInternalServerError
	}

	return &session, nil
}

func (r *SessionStoreImpl) DeleteAllForUser(ctx context.Context, accountID string) error {
	filter := bson.M{"accountId": accountID}

	_, err := r.sessionCollection.DeleteMany(ctx, filter)
	if err != nil {
		return apperrors.ErrInternalServerError
	}

	return nil
}

func (r *SessionStoreImpl) DeleteAllWithVersion(ctx context.Context, version string) error {
	filter := bson.M{"tokenVersion": version}

	_, err := r.sessionCollection.DeleteMany(ctx, filter)
	if err != nil {
		return apperrors.ErrInternalServerError
	}

	return nil
}
