package store

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/auth/model"
)

type SessionStore interface {
	FindByToken(ctx context.Context, token string) (*model.Session, error)
	FindByRefreshToken(ctx context.Context, refreshToken string) (*model.Session, error)
	FindByAccountID(ctx context.Context, accountID string) (*model.Session, error)
	FindOneAndUpdate(ctx context.Context, session *model.Session) error
	Delete(ctx context.Context, sessionID string) error

	// Session management methods
	DeleteAllForUser(ctx context.Context, accountID string) error
	DeleteAllWithVersion(ctx context.Context, version string) error
}
