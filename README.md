# Meeting Dashboard Backend

## คำอธิบายโปรเจค

โปรเจคนี้เป็นระบบ Backend สำหรับ Meeting Dashboard ที่ช่วยจัดการข้อมูลการประชุม เช่น การเพิ่มความคิดเห็น การอัปเดตสถานะ และการจัดการผู้ใช้ โดยใช้ภาษา Go และ MongoDB เป็นฐานข้อมูลหลัก

## โครงสร้างโปรเจค

- **cmd/server**: ไฟล์เริ่มต้นของโปรเจค
- **config**: การตั้งค่าต่าง ๆ ของโปรเจค
- **database**: การเชื่อมต่อกับฐานข้อมูล MongoDB
- **internal**: โค้ดหลักของโปรเจคแบ่งเป็นโมดูลต่าง ๆ เช่น
  - **auth**: การจัดการ Authentication และ JWT
  - **meetings**: การจัดการข้อมูลการประชุม เช่น ความคิดเห็นและสถานะ
  - **migration**: การย้ายข้อมูลจากระบบอื่น
  - **users**: การจัดการข้อมูลผู้ใช้
- **pkg**: Utility ต่าง ๆ เช่น Logger และ Error Handler

## การติดตั้ง

1. ติดตั้ง Go (เวอร์ชัน 1.24.2 หรือใหม่กว่า)
2. Clone โปรเจคนี้:
   ```bash
   git clone https://git.inet.co.th/meeting-dashboard/meeting-dashboard-backend.git
   cd meeting-dashboard-backend
   ```
3. ติดตั้ง Dependencies:
   ```bash
   go mod tidy
   ```

## การใช้งาน

1. สร้างไฟล์ `.env` และตั้งค่าต่าง ๆ เช่น MongoDB URI, JWT Secret Key
2. รันโปรเจค:
   ```bash
   go run cmd/server/main.go
   ```
