sequenceDiagram
title Meeting Dashboard - Login Flow

    actor Client
    participant AuthHandler
    participant AuthService
    participant OneThService
    participant AccountStore
    participant UserStore
    participant JWTService
    participant SessionStore
    participant MongoD<PERSON> as database

    Client->>AuthHandler: POST /api/v1/auth/signin<br>(username, password)
    activate Auth<PERSON>andler

    AuthHandler->>AuthService: SignIn(input)
    activate AuthService

    AuthService->>OneThService: PasswordGrant(username, password)
    activate OneThService
    OneThService->>OneThService: Authentication with one.th API
    OneThService-->>AuthService: OneThResponse (accountID, etc.)
    deactivate OneThService

    AuthService->>AccountStore: FindByProvider(one.th, accountID)
    activate AccountStore
    AccountStore->>MongoDB: Query accounts collection
    MongoDB-->>AccountStore: Account data
    AccountStore-->>AuthService: Account entity
    deactivate AccountStore

    AuthService->>UserStore: FindByUserID(userID)
    activate UserStore
    UserStore->>MongoDB: Query users collection
    MongoDB-->>UserStore: User data
    UserStore-->>AuthService: User entity
    deactivate UserStore

    alt tokens changed
        AuthService->>AccountStore: Update(account)
        AccountStore->>MongoDB: Update accounts collection
    end

    AuthService->>JWTService: GenerateToken(user)
    activate JWTService
    JWTService-->>AuthService: token, expiryTime
    deactivate JWTService

    AuthService->>SessionStore: Create(session)
    activate SessionStore
    SessionStore->>MongoDB: Insert into sessions collection
    SessionStore-->>AuthService: success
    deactivate SessionStore

    AuthService-->>AuthHandler: SignInOutput
    deactivate AuthService

    AuthHandler-->>Client: SignInResponse<br>(token, expiresAt, user)
    deactivate AuthHandler
