package database

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"git.inet.co.th/meeting-dashboard-backend/pkg/logger"
)

type MongoDB struct {
	Client *mongo.Client
	DB     *mongo.Database
}

func Connect(ctx context.Context, uri string, dbName string) (*MongoDB, error) {
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(uri))
	if err != nil {
		return nil, err
	}

	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		return nil, err
	}
	logger.Log.Info().Msg("Successfully connected and pinged MongoDB.")

	db := client.Database(dbName)

	return &MongoDB{
		Client: client,
		DB:     db,
	}, nil
}

func MustConnect(ctx context.Context, uri string, dbName string) *MongoDB {
	mongodb, err := Connect(ctx, uri, dbName)
	if err != nil {
		logger.Log.Fatal().Err(err).Msg("Failed to connect to MongoDB")
	}
	return mongodb
}

func (m *MongoDB) Disconnect(ctx context.Context) error {
	err := m.Client.Disconnect(ctx)
	if err != nil {
		return err
	}
	logger.Log.Info().Msg("MongoDB connection closed.")
	return nil
}

func EnsureIndexes(ctx context.Context, db *mongo.Database) error {
	_, err := db.Collection("users").Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "accountId", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{{Key: "email", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		return err
	}

	_, err = db.Collection("sessions").Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "sessionId", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{{Key: "token", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{{Key: "refreshToken", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{{Key: "accountId", Value: 1}},
			Options: options.Index(),
		},
		{
			Keys:    bson.D{{Key: "tokenVersion", Value: 1}},
			Options: options.Index(),
		},
	})
	if err != nil {
		return err
	}

	_, err = db.Collection("meeting_join").Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "meet_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		return err
	}

	_, err = db.Collection("meeting").Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "meet_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		return err
	}

	_, err = db.Collection("meeting_period").Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "meet_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		return err
	}

	_, err = db.Collection("meeting_metadata").Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "meet_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		return err
	}

	return nil
}

func MustEnsureIndexes(ctx context.Context, db *mongo.Database) {
	if err := EnsureIndexes(ctx, db); err != nil {
		logger.Log.Fatal().Err(err).Msg("Failed to ensure MongoDB indexes")
	}
}
