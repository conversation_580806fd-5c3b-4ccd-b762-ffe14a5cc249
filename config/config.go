package config

import (
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"

	"git.inet.co.th/meeting-dashboard-backend/pkg/logger"
)

type Config struct {
	MongoURI           string
	DBName             string
	Port               string
	OneThURL           string
	OneThClientID      string
	OneThSecret        string
	RAOneThURL         string
	RAOneThBearerToken string
	MeetVisitURL       string
	MeetVisitClientID  string
	MeetVisitSecretKey string
	JWTSecretKey       string
	JWTExpiry          time.Duration
	JWTVersion         string
	JWTRefreshExpiry   time.Duration
	RSAPrivateKeyPEM   string
}

func LoadConfig() Config {
	if err := godotenv.Load(); err != nil {
		logger.Log.Warn().Err(err).Msg("Error loading .env file")
	}

	mongoURI := os.Getenv("MONGO_URI")
	dbName := os.Getenv("DB_NAME")
	port := os.Getenv("PORT")

	oneThURL := os.Getenv("ONETH_URL")
	oneThClientID := os.Getenv("ONETH_CLIENT_ID")
	oneThSecret := os.Getenv("ONETH_CLIENT_SECRET")

	raOneThURL := os.Getenv("RA_ONETH_URL")
	raOneThBearerToken := os.Getenv("RA_ONETH_BEARER_TOKEN")

	meetVisitURL := os.Getenv("MEET_VISIT_URL")
	meetVisitClientID := os.Getenv("MEET_VISIT_CLIENT_ID")
	meetVisitSecretKey := os.Getenv("MEET_VISIT_CLIENT_SECRET")

	jwtSecret := os.Getenv("JWT_SECRET_KEY")
	jwtVersion := os.Getenv("JWT_VERSION")

	jwtExpiryStr := os.Getenv("JWT_EXPIRY_HOURS")
	jwtRefreshExpiryStr := os.Getenv("JWT_REFRESH_EXPIRY_DAYS")

	rsaPrivateKeyPEM := os.Getenv("RSA_PRIVATE_KEY_PEM")

	jwtExpiryHours, err := strconv.Atoi(jwtExpiryStr)
	if err != nil {
		jwtExpiryHours = 2
	}

	jwtRefreshExpiryDays, err := strconv.Atoi(jwtRefreshExpiryStr)
	if err != nil {
		jwtRefreshExpiryDays = 7
	}

	jwtExpiry := time.Duration(jwtExpiryHours) * time.Minute
	jwtRefreshExpiry := time.Duration(jwtRefreshExpiryDays) * 24 * time.Hour

	return Config{
		MongoURI:           mongoURI,
		DBName:             dbName,
		Port:               port,
		OneThURL:           oneThURL,
		OneThClientID:      oneThClientID,
		OneThSecret:        oneThSecret,
		RAOneThURL:         raOneThURL,
		RAOneThBearerToken: raOneThBearerToken,
		JWTSecretKey:       jwtSecret,
		JWTExpiry:          jwtExpiry,
		JWTVersion:         jwtVersion,
		JWTRefreshExpiry:   jwtRefreshExpiry,
		MeetVisitURL:       meetVisitURL,
		MeetVisitClientID:  meetVisitClientID,
		MeetVisitSecretKey: meetVisitSecretKey,
		RSAPrivateKeyPEM:   rsaPrivateKeyPEM,
	}
}
