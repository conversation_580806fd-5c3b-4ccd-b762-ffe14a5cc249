package main

import (
	"context"
	"time"

	"git.inet.co.th/meeting-dashboard-backend/config"
	"git.inet.co.th/meeting-dashboard-backend/database"
	"git.inet.co.th/meeting-dashboard-backend/internal/bootstrap"
	"git.inet.co.th/meeting-dashboard-backend/pkg/logger"
)

func main() {
	logger.Init()

	cfg := config.LoadConfig()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	mongodb := database.MustConnect(ctx, cfg.MongoURI, cfg.DBName)
	database.MustEnsureIndexes(context.Background(), mongodb.DB)
	defer mongodb.Disconnect(context.Background())

	authModule := bootstrap.SetupAuth(mongodb.DB, cfg)
	userModule := bootstrap.SetupUser(mongodb.DB, cfg)
	migrationModule := bootstrap.SetupMigration(mongodb.DB, cfg)
	meetingModule := bootstrap.SetupMeeting(mongodb.DB, cfg)

	apiModule := bootstrap.SetupAPI(authModule, userModule, migrationModule, meetingModule)
	appModule := bootstrap.SetupApp(apiModule, cfg.Port)

	appModule.Start()
}
