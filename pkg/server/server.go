package server

import (
	"context"
	"fmt"
	"log"
	"net/http"

	"git.inet.co.th/meeting-dashboard-backend/pkg/router"
)

type Server struct {
	router     *router.Router
	httpServer *http.Server
	port       string
}

func NewServer(router *router.Router, port string) *Server {
	return &Server{
		router: router,
		port:   port,
	}
}

func (s *Server) Start() error {
	addr := fmt.Sprintf(":%s", s.port)
	s.httpServer = &http.Server{
		Addr:    addr,
		Handler: s.router.Engine(),
	}

	log.Printf("Server starting on port %s", s.port)
	return s.httpServer.ListenAndServe()
}

func (s *Server) Shutdown(ctx context.Context) error {
	log.Println("Server shutting down...")
	return s.httpServer.Shutdown(ctx)
}
