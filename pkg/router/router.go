package router

import (
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/rs/cors"

	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"
)

type Router struct {
	engine *gin.Engine
}

func NewRouter() *Router {
	ginMode := os.Getenv("GIN_MODE")
	gin.SetMode(ginMode)
	r := gin.New()

	// Add CORS middleware
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "https://localhost:3000", "https://platformmeeting.inet.co.th"},
		AllowedMethods:   []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodPatch, http.MethodDelete, http.MethodOptions},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: true,
		MaxAge:           300, // Maximum value not ignored by any of major browsers
	})

	// Wrap the cors handler into a Gin middleware function
	r.Use(func(ctx *gin.Context) {
		c.HandlerFunc(ctx.Writer, ctx.Request)

		// Abort processing if the CORS middleware wrote a response (e.g., for OPTIONS preflight)
		if ctx.Writer.Status() != http.StatusOK && ctx.Writer.Status() != 0 {
			ctx.Abort()
			return
		}
		ctx.Next()
	})

	r.Use(middleware.RequestLoggerMiddleware())
	r.Use(gin.Recovery())
	r.Use(middleware.ErrorHandlerMiddleware())

	return &Router{engine: r}
}

func (r *Router) Engine() *gin.Engine {
	return r.engine
}

func (r *Router) Group(path string) *gin.RouterGroup {
	return r.engine.Group(path)
}
