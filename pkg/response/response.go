package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type SuccessResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

type ErrorResponse struct {
	Status string `json:"status"`
	Type   string `json:"type,omitempty"`
	Error  string `json:"error"`
}

func SendSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	response := SuccessResponse{
		Status:  "success",
		Message: message,
		Data:    data,
	}
	c.JSON(statusCode, response)
	// Success responses usually don't need Abort, but if they should stop middleware chain:
	// c.AbortWithStatusJSON(statusCode, response)
}

func SendOK(c *gin.Context, message string, data interface{}) {
	SendSuccess(c, http.StatusOK, message, data)
}

func SendCreated(c *gin.Context, message string, data interface{}) {
	SendSuccess(c, http.StatusCreated, message, data)
}

func SendNoContent(c *gin.Context) {
	c.AbortWithStatus(http.StatusNoContent) // Use AbortWithStatus to stop middleware chain
}

func SendError(c *gin.Context, statusCode int, typeErr, errorMessage string) {
	response := ErrorResponse{
		Status: "error",
		Type:   typeErr,
		Error:  errorMessage,
	}
	// Use AbortWithStatusJSON to stop the middleware chain and send the error response
	c.AbortWithStatusJSON(statusCode, response)
}
