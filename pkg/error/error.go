package error

import (
	"errors"

	"github.com/gin-gonic/gin"
)

type AppError struct {
	Code    int    // HTTP status code
	Message string // Message to return to client
	Err     error  // Original error (optional)
}

func (e *AppError) Error() string {
	return e.Message
}

func (e *AppError) Unwrap() error {
	return e.Err
}

// สร้าง AppError ใหม่
func NewAppError(code int, msg string, err error) *AppError {
	return &AppError{
		Code:    code,
		Message: msg,
		Err:     err,
	}
}

// Helper Error Variables
var (
	ErrNotFound       = errors.New("resource not found")
	ErrInternalServer = errors.New("internal server error")
	ErrInvalidRequest = errors.New("invalid request")
)

// ฟังก์ชั่น map error มาตรฐาน ไปเป็น AppError
func MapError(err error) *AppError {
	switch {
	case errors.Is(err, ErrNotFound):
		return NewAppError(404, "resource not found", err)
	case errors.Is(err, ErrInvalidRequest):
		return NewAppError(400, "invalid request", err)
	default:
		return NewAppError(500, "internal server error", err)
	}
}

func RespondError(c *gin.Context, appErr *AppError) {
	c.JSON(appErr.Code, gin.H{
		"status":  "error",
		"error":   appErr.Message,
		"message": appErr.Err.Error(),
	})
}
